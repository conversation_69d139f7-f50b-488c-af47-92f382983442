import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Box,
  Button,
  Chip,
  Alert,
  Snackbar,
  useTheme,
  useMediaQuery,
  Paper
} from '@mui/material';
import {
  Close as CloseIcon,
  ContentCopy as CopyIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

interface PlantAnalysisPopupProps {
  open: boolean;
  onClose: () => void;
  analysisResult: string;
  language: string;
}

interface ParsedAnalysis {
  plantId?: string;
  healthAssessment?: string;
  symptoms?: string[];
  diagnosis?: string;
  treatment?: string[];
  prevention?: string[];
  solutions?: string;
  timeline?: string;
  severity?: string;
  isError?: boolean;
  errorMessage?: string;
}

const PlantAnalysisPopup: React.FC<PlantAnalysisPopupProps> = ({
  open,
  onClose,
  analysisResult,
  language
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [copySuccess, setCopySuccess] = useState(false);

  // Parse the analysis result into structured data
  const parseAnalysis = (text: string): ParsedAnalysis => {
    if (text.includes('ERROR: This appears to be a non-plant image')) {
      return {
        isError: true,
        errorMessage: text.replace('ERROR: ', '')
      };
    }

    const sections: ParsedAnalysis = {};
    
    // Extract plant identification
    const plantIdMatch = text.match(/\*\*🌿 PLANT IDENTIFICATION\*\*([\s\S]*?)(?=\*\*|$)/);
    if (plantIdMatch) {
      sections.plantId = plantIdMatch[1].trim();
    }

    // Extract health assessment
    const healthMatch = text.match(/\*\*🔍 HEALTH ASSESSMENT\*\*([\s\S]*?)(?=\*\*|$)/);
    if (healthMatch) {
      sections.healthAssessment = healthMatch[1].trim();
    }

    // Extract symptoms
    const symptomsMatch = text.match(/\*\*⚠️ SYMPTOMS DETECTED\*\*([\s\S]*?)(?=\*\*|$)/);
    if (symptomsMatch) {
      sections.symptoms = symptomsMatch[1]
        .split('\n')
        .filter(line => line.trim().startsWith('-'))
        .map(line => line.replace(/^-\s*/, '').trim());
    }

    // Extract diagnosis
    const diagnosisMatch = text.match(/\*\*🦠 DIAGNOSIS\*\*([\s\S]*?)(?=\*\*|$)/);
    if (diagnosisMatch) {
      sections.diagnosis = diagnosisMatch[1].trim();
    }

    // Extract treatment
    const treatmentMatch = text.match(/\*\*💊 IMMEDIATE TREATMENT\*\*([\s\S]*?)(?=\*\*|$)/);
    if (treatmentMatch) {
      sections.treatment = treatmentMatch[1]
        .split('\n')
        .filter(line => line.trim().startsWith('-') || line.trim().startsWith('Step'))
        .map(line => line.replace(/^(-\s*|Step \d+:\s*)/, '').trim());
    }

    // Extract prevention
    const preventionMatch = text.match(/\*\*🛡️ PREVENTION MEASURES\*\*([\s\S]*?)(?=\*\*|$)/);
    if (preventionMatch) {
      sections.prevention = preventionMatch[1]
        .split('\n')
        .filter(line => line.trim().startsWith('-'))
        .map(line => line.replace(/^-\s*/, '').trim());
    }

    // Extract detailed solutions
    const solutionsMatch = text.match(/\*\*📋 DETAILED SOLUTIONS\*\*([\s\S]*?)(?=\*\*|$)/);
    if (solutionsMatch) {
      sections.solutions = solutionsMatch[1].trim();
    }

    // Extract timeline
    const timelineMatch = text.match(/\*\*⏰ TIMELINE\*\*([\s\S]*?)(?=\*\*|$)/);
    if (timelineMatch) {
      sections.timeline = timelineMatch[1].trim();
    }

    // Extract severity
    const severityMatch = text.match(/\*\*🚨 SEVERITY LEVEL\*\*([\s\S]*?)(?=\*\*|$)/);
    if (severityMatch) {
      sections.severity = severityMatch[1].trim();
    }

    return sections;
  };

  const analysis = parseAnalysis(analysisResult);

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(analysisResult);
      setCopySuccess(true);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = analysisResult;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopySuccess(true);
    }
  };

  const getSeverityColor = (severity: string) => {
    if (severity.includes('Critical') || severity.includes('High')) return 'error';
    if (severity.includes('Medium')) return 'warning';
    return 'success';
  };

  const getSeverityIcon = (severity: string) => {
    if (severity.includes('Critical') || severity.includes('High')) return <ErrorIcon />;
    if (severity.includes('Medium')) return <WarningIcon />;
    return <CheckCircleIcon />;
  };

  if (analysis.isError) {
    return (
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="sm"
        fullWidth
        fullScreen={false}
        PaperProps={{
          sx: {
            borderRadius: 3,
            maxHeight: isMobile ? '85vh' : '90vh',
            width: isMobile ? '95vw' : 'auto',
            margin: isMobile ? '8px' : '32px'
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          bgcolor: 'error.main',
          color: 'white'
        }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ErrorIcon />
            {language === 'kn' ? 'ಚಿತ್ರ ದೋಷ' : language === 'hi' ? 'छवि त्रुटि' : 'Image Error'}
          </Typography>
          <IconButton onClick={onClose} sx={{ color: 'white' }}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Alert severity="error" sx={{ mb: 2 }}>
            {analysis.errorMessage}
          </Alert>
          <Typography variant="body2" color="text.secondary">
            {language === 'kn' 
              ? 'ದಯವಿಟ್ಟು ಸಸ್ಯದ ಎಲೆ, ಕಾಂಡ ಅಥವಾ ಹೂವಿನ ಸ್ಪಷ್ಟ ಚಿತ್ರವನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ.'
              : language === 'hi' 
              ? 'कृपया पौधे के पत्ते, तने या फूल की स्पष्ट छवि अपलोड करें।'
              : 'Please upload a clear image of a plant leaf, stem, or flower.'
            }
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={onClose} variant="contained" color="primary">
            {language === 'kn' ? 'ಮುಚ್ಚಿ' : language === 'hi' ? 'बंद करें' : 'Close'}
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        fullScreen={false}
        PaperProps={{
          sx: {
            borderRadius: 3,
            maxHeight: isMobile ? '85vh' : '90vh',
            width: isMobile ? '95vw' : 'auto',
            margin: isMobile ? '8px' : '32px'
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          bgcolor: 'primary.main',
          color: 'white',
          pb: 1
        }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            🌿 {language === 'kn' ? 'ಸಸ್ಯ ವಿಶ್ಲೇಷಣೆ' : language === 'hi' ? 'पौधा विश्लेषण' : 'Plant Analysis'}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton onClick={handleCopyToClipboard} sx={{ color: 'white' }} size="small">
              <CopyIcon />
            </IconButton>
            <IconButton onClick={onClose} sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 0 }}>
          {/* Severity Alert */}
          {analysis.severity && (
            <Alert 
              severity={getSeverityColor(analysis.severity)} 
              icon={getSeverityIcon(analysis.severity)}
              sx={{ m: 2, borderRadius: 2 }}
            >
              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                {analysis.severity}
              </Typography>
            </Alert>
          )}

          {/* Plant Identification */}
          {analysis.plantId && (
            <Paper elevation={0} sx={{ m: 2, p: 2, bgcolor: 'rgba(76, 175, 80, 0.05)' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
                🌿 Plant Identification
              </Typography>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                {analysis.plantId}
              </Typography>
            </Paper>
          )}

          {/* Health Assessment */}
          {analysis.healthAssessment && (
            <Paper elevation={0} sx={{ m: 2, p: 2, bgcolor: 'rgba(33, 150, 243, 0.05)' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'info.main', mb: 1 }}>
                🔍 Health Assessment
              </Typography>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                {analysis.healthAssessment}
              </Typography>
            </Paper>
          )}

          {/* Diagnosis */}
          {analysis.diagnosis && (
            <Paper elevation={0} sx={{ m: 2, p: 2, bgcolor: 'rgba(255, 152, 0, 0.05)', border: 1, borderColor: 'rgba(255, 152, 0, 0.2)' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'warning.dark', mb: 1 }}>
                🦠 Diagnosis
              </Typography>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                {analysis.diagnosis}
              </Typography>
            </Paper>
          )}

          {/* Symptoms */}
          {analysis.symptoms && analysis.symptoms.length > 0 && (
            <Paper elevation={0} sx={{ m: 2, p: 2, bgcolor: 'rgba(244, 67, 54, 0.05)', border: 1, borderColor: 'rgba(244, 67, 54, 0.2)' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'error.dark', mb: 1 }}>
                ⚠️ Symptoms
              </Typography>
              <Box>
                {analysis.symptoms.map((symptom, index) => (
                  <Chip
                    key={index}
                    label={symptom}
                    size="small"
                    sx={{ m: 0.5, bgcolor: 'rgba(244, 67, 54, 0.1)' }}
                  />
                ))}
              </Box>
            </Paper>
          )}

          {/* Treatment */}
          {analysis.treatment && analysis.treatment.length > 0 && (
            <Paper elevation={0} sx={{ m: 2, p: 2, bgcolor: 'rgba(76, 175, 80, 0.05)', border: 1, borderColor: 'rgba(76, 175, 80, 0.2)' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'success.dark', mb: 1 }}>
                💊 Immediate Treatment
              </Typography>
              {analysis.treatment.map((step, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                  <Typography variant="body2" sx={{
                    bgcolor: 'success.main',
                    color: 'white',
                    borderRadius: '50%',
                    width: 20,
                    height: 20,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '0.75rem',
                    mr: 1,
                    mt: 0.25,
                    flexShrink: 0
                  }}>
                    {index + 1}
                  </Typography>
                  <Typography variant="body2">{step}</Typography>
                </Box>
              ))}
            </Paper>
          )}

          {/* Prevention */}
          {analysis.prevention && analysis.prevention.length > 0 && (
            <Paper elevation={0} sx={{ m: 2, p: 2, bgcolor: 'rgba(33, 150, 243, 0.05)', border: 1, borderColor: 'rgba(33, 150, 243, 0.2)' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'info.dark', mb: 1 }}>
                🛡️ Prevention Measures
              </Typography>
              {analysis.prevention.map((tip, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                  <Typography variant="body2" sx={{ color: 'info.main', mr: 1, mt: 0.25 }}>
                    •
                  </Typography>
                  <Typography variant="body2">{tip}</Typography>
                </Box>
              ))}
            </Paper>
          )}

          {/* Detailed Solutions */}
          {analysis.solutions && (
            <Paper elevation={0} sx={{ m: 2, p: 2, bgcolor: 'rgba(156, 39, 176, 0.05)', border: 1, borderColor: 'rgba(156, 39, 176, 0.2)' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'purple', mb: 1 }}>
                📋 Detailed Solutions
              </Typography>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                {analysis.solutions}
              </Typography>
            </Paper>
          )}

          {/* Timeline */}
          {analysis.timeline && (
            <Paper elevation={0} sx={{ m: 2, p: 2, bgcolor: 'rgba(255, 193, 7, 0.05)', border: 1, borderColor: 'rgba(255, 193, 7, 0.2)' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'orange', mb: 1 }}>
                ⏰ Timeline
              </Typography>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                {analysis.timeline}
              </Typography>
            </Paper>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 2, justifyContent: 'center' }}>
          <Button onClick={onClose} variant="contained" color="primary" size="large">
            {language === 'kn' ? 'ಮುಚ್ಚಿ' : language === 'hi' ? 'बंद करें' : 'Close'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={copySuccess}
        autoHideDuration={3000}
        onClose={() => setCopySuccess(false)}
        message={
          language === 'kn' ? 'ವಿಶ್ಲೇಷಣೆಯನ್ನು ಕ್ಲಿಪ್‌ಬೋರ್ಡ್‌ಗೆ ನಕಲಿಸಲಾಗಿದೆ' :
          language === 'hi' ? 'विश्लेषण क्लिपबोर्ड पर कॉपी किया गया' :
          'Analysis copied to clipboard'
        }
      />
    </>
  );
};

export default PlantAnalysisPopup;
