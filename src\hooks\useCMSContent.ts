import { useState, useEffect } from 'react';
import { logError } from '../utils/logger';

// CMS Content Types
export interface CMSSettings {
  site: {
    title: string;
    description: string;
    url: string;
    logo?: string;
    favicon?: string;
  };
  contact: {
    phone: string;
    email: string;
    address: string;
    workingHours: string;
    gstNumber: string;
  };
  social: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    youtube?: string;
  };
}

export interface PaymentSettings {
  registration: {
    baseAmount: number;
    gstPercentage: number;
    totalAmount: number;
    currency: string;
    currencySymbol: string;
  };
  gateway: {
    name: string;
    testMode: boolean;
    timeout: number;
  };
  methods: {
    creditCards: boolean;
    debitCards: boolean;
    netBanking: boolean;
    upi: boolean;
    wallets: boolean;
  };
}

export interface ReceiptSettings {
  header: {
    companyName: string;
    title: string;
    gstNumber: string;
    website: string;
    phone: string;
  };
  footer: {
    thankYou: string;
    successMessage: string;
    supportMessage: string;
    tagline: string;
    signatureNote: string;
  };
  styling: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    fontSize: number;
  };
}

export interface UIContent {
  buttons: {
    primary: Record<string, string>;
    secondary: Record<string, string>;
    loading: Record<string, string>;
  };
  messages: {
    success: Record<string, string>;
    error: Record<string, string>;
    validation: Record<string, string>;
  };
  labels: {
    fields: Record<string, string>;
    placeholders: Record<string, string>;
  };
}

export interface FeatureFlags {
  payment: {
    enablePayment: boolean;
    enableReceipt: boolean;
    enableEmailNotifications: boolean;
    enableSmsNotifications: boolean;
  };
  form: {
    enableMultiLanguage: boolean;
    enableAutoSave: boolean;
    enableProgressBar: boolean;
    enableValidation: boolean;
  };
  ui: {
    enableDarkMode: boolean;
    enableAnimations: boolean;
    enableLoadingStates: boolean;
    enableTooltips: boolean;
  };
}

// Blog Content Types
export interface BlogPost {
  id: number;
  title: {
    en: string;
    kn: string;
    hi: string;
  };
  excerpt: {
    en: string;
    kn: string;
    hi: string;
  };
  content?: {
    en: string;
    kn: string;
    hi: string;
  };
  category: {
    en: string;
    kn: string;
    hi: string;
  };
  author: {
    en: string;
    kn: string;
    hi: string;
  };
  date: string;
  readTime: {
    en: string;
    kn: string;
    hi: string;
  };
  image: string;
  featured: boolean;
  tags?: string[];
}

export interface BlogContent {
  hero: {
    title: {
      en: string;
      kn: string;
      hi: string;
    };
    subtitle: {
      en: string;
      kn: string;
      hi: string;
    };
    backgroundImage: string;
  };
  posts: BlogPost[];
  categories: Array<{
    value: string;
    label: {
      en: string;
      kn: string;
      hi: string;
    };
  }>;
}

// Custom hook to load CMS content
export const useCMSContent = () => {
  const [cmsSettings, setCMSSettings] = useState<CMSSettings | null>(null);
  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings | null>(null);
  const [receiptSettings, setReceiptSettings] = useState<ReceiptSettings | null>(null);
  const [uiContent, setUIContent] = useState<UIContent | null>(null);
  const [featureFlags, setFeatureFlags] = useState<FeatureFlags | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadCMSContent = async () => {
      try {
        setLoading(true);
        
        // Load all CMS content files
        const [
          cmsResponse,
          paymentResponse,
          receiptResponse,
          buttonsResponse,
          messagesResponse,
          labelsResponse,
          featuresResponse
        ] = await Promise.all([
          import('../config/cms-settings.json'),
          import('../config/payment-settings.json'),
          import('../config/receipt-settings.json'),
          import('../content/ui/buttons.json'),
          import('../content/ui/messages.json'),
          import('../content/ui/labels.json'),
          import('../config/features.json')
        ]);

        setCMSSettings(cmsResponse.default);
        setPaymentSettings(paymentResponse.default);
        setReceiptSettings(receiptResponse.default);
        setUIContent({
          buttons: buttonsResponse.default,
          messages: messagesResponse.default,
          labels: labelsResponse.default
        });
        setFeatureFlags(featuresResponse.default);
        
        setError(null);
      } catch (err) {
        // Production-safe error logging
        logError('Failed to load CMS content', err);
        setError('Failed to load content');
      } finally {
        setLoading(false);
      }
    };

    loadCMSContent();
  }, []);

  return {
    cmsSettings,
    paymentSettings,
    receiptSettings,
    uiContent,
    featureFlags,
    loading,
    error
  };
};

// Helper hooks for specific content types
export const useUIContent = () => {
  const { uiContent, loading, error } = useCMSContent();
  return { uiContent, loading, error };
};

export const usePaymentSettings = () => {
  const { paymentSettings, loading, error } = useCMSContent();
  return { paymentSettings, loading, error };
};

export const useReceiptSettings = () => {
  const { receiptSettings, loading, error } = useCMSContent();
  return { receiptSettings, loading, error };
};

export const useFeatureFlags = () => {
  const { featureFlags, loading, error } = useCMSContent();
  return { featureFlags, loading, error };
};

export const useSiteSettings = () => {
  const { cmsSettings, loading, error } = useCMSContent();
  return { siteSettings: cmsSettings, loading, error };
};

// Blog content hook with support for individual posts
export const useBlogContent = () => {
  const [blogContent, setBlogContent] = useState<BlogContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadBlogContent = async () => {
      try {
        setLoading(true);

        // Load blog page settings
        const blogResponse = await import('../content/blog/content.json');
        const blogSettings = blogResponse.default;

        // Load individual blog posts (fallback to content.json posts if individual files don't exist)
        let posts: BlogPost[] = [];

        try {
          // Try to load individual post files
          const postModules = await Promise.allSettled([
            import('../content/blog/posts/2024-01-15-sustainable-farming-practices.md'),
            import('../content/blog/posts/2024-01-10-iot-technology-agriculture.md')
          ]);

          posts = postModules
            .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
            .map((result, index) => {
              const post = result.value.default || result.value;
              return {
                id: index + 1,
                title: post.title,
                excerpt: post.excerpt,
                content: post.content,
                category: {
                  en: post.category === 'sustainable' ? 'Sustainable Farming' :
                      post.category === 'technology' ? 'Technology' :
                      post.category === 'organic' ? 'Organic Farming' :
                      post.category === 'water' ? 'Water Management' : 'General',
                  kn: post.category === 'sustainable' ? 'ಸುಸ್ಥಿರ ಕೃಷಿ' :
                      post.category === 'technology' ? 'ತಂತ್ರಜ್ಞಾನ' :
                      post.category === 'organic' ? 'ಸಾವಯವ ಕೃಷಿ' :
                      post.category === 'water' ? 'ನೀರಿನ ನಿರ್ವಹಣೆ' : 'ಸಾಮಾನ್ಯ',
                  hi: post.category === 'sustainable' ? 'स्थायी खेती' :
                      post.category === 'technology' ? 'तकनीक' :
                      post.category === 'organic' ? 'जैविक खेती' :
                      post.category === 'water' ? 'जल प्रबंधन' : 'सामान्य'
                },
                author: post.author,
                date: post.date,
                readTime: post.readTime,
                image: post.image,
                featured: post.featured || false,
                tags: post.tags || []
              };
            });
        } catch (postError) {
          // Fallback to legacy posts from content.json if available
          if (blogSettings.posts && Array.isArray(blogSettings.posts)) {
            posts = blogSettings.posts;
          }
        }

        // If no posts found, use sample data
        if (posts.length === 0) {
          posts = [
            {
              id: 1,
              title: {
                en: 'Sustainable Farming Practices for Modern Agriculture',
                kn: 'ಆಧುನಿಕ ಕೃಷಿಗಾಗಿ ಸುಸ್ಥಿರ ಕೃಷಿ ಅಭ್ಯಾಸಗಳು',
                hi: 'आधुनिक कृषि के लिए स्थायी खेती प्रथाएं'
              },
              excerpt: {
                en: 'Discover innovative sustainable farming techniques that are revolutionizing modern agriculture while preserving our environment.',
                kn: 'ನಮ್ಮ ಪರಿಸರವನ್ನು ಸಂರಕ್ಷಿಸುತ್ತಾ ಆಧುನಿಕ ಕೃಷಿಯನ್ನು ಕ್ರಾಂತಿಕಾರಕಗೊಳಿಸುತ್ತಿರುವ ನವೀನ ಸುಸ್ಥಿರ ಕೃಷಿ ತಂತ್ರಗಳನ್ನು ಅನ್ವೇಷಿಸಿ.',
                hi: 'हमारे पर्यावरण को संरक्षित करते हुए आधुनिक कृषि को क्रांतिकारी बना रही नवीन स्थायी खेती तकनीकों की खोज करें।'
              },
              category: {
                en: 'Sustainable Farming',
                kn: 'ಸುಸ್ಥಿರ ಕೃಷಿ',
                hi: 'स्थायी खेती'
              },
              author: {
                en: 'Dr. Rajesh Kumar',
                kn: 'ಡಾ. ರಾಜೇಶ್ ಕುಮಾರ್',
                hi: 'डॉ. राजेश कुमार'
              },
              date: '2024-01-15',
              readTime: {
                en: '5 min read',
                kn: '5 ನಿಮಿಷ ಓದು',
                hi: '5 मिनट पढ़ें'
              },
              image: '/darvi-images/field1.png',
              featured: true,
              tags: ['sustainable', 'farming', 'environment']
            }
          ];
        }

        const finalBlogContent: BlogContent = {
          hero: blogSettings.hero || {
            title: {
              en: 'Agricultural Blog',
              kn: 'ಕೃಷಿ ಬ್ಲಾಗ್',
              hi: 'कृषि ब्लॉग'
            },
            subtitle: {
              en: 'Insights, innovations, and best practices in modern agriculture',
              kn: 'ಆಧುನಿಕ ಕೃಷಿಯಲ್ಲಿ ಒಳನೋಟಗಳು, ನವೀಕರಣಗಳು ಮತ್ತು ಉತ್ತಮ ಅಭ್ಯಾಸಗಳು',
              hi: 'आधुनिक कृषि में अंतर्दृष्टि, नवाचार और सर्वोत्तम प्रथाएं'
            },
            backgroundImage: '/darvi-images/field1.png'
          },
          posts,
          categories: blogSettings.categories || [
            { value: 'all', label: { en: 'All', kn: 'ಎಲ್ಲಾ', hi: 'सभी' } },
            { value: 'sustainable', label: { en: 'Sustainable Farming', kn: 'ಸುಸ್ಥಿರ ಕೃಷಿ', hi: 'स्थायी खेती' } },
            { value: 'technology', label: { en: 'Technology', kn: 'ತಂತ್ರಜ್ಞಾನ', hi: 'तकनीक' } },
            { value: 'organic', label: { en: 'Organic Farming', kn: 'ಸಾವಯವ ಕೃಷಿ', hi: 'जैविक खेती' } },
            { value: 'water', label: { en: 'Water Management', kn: 'ನೀರಿನ ನಿರ್ವಹಣೆ', hi: 'जल प्रबंधन' } }
          ]
        };

        setBlogContent(finalBlogContent);
        setError(null);
      } catch (err) {
        // Production-safe error logging
        logError('Failed to load blog content', err);
        setError('Failed to load blog content');
      } finally {
        setLoading(false);
      }
    };

    loadBlogContent();
  }, []);

  return {
    blogContent,
    loading,
    error
  };
};
