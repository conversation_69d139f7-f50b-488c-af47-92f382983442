import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  <PERSON>ton,
  Box,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Avatar,
  Divider,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Alert,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Close as CloseIcon,
  Visibility as PreviewIcon,
  Edit as EditIcon,
  Publish as PublishIcon,
  Schedule as ScheduleIcon,
  Language as LanguageIcon,
  Share as ShareIcon,
  Bookmark as BookmarkIcon
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';
import { BlogPost } from '../hooks/useCMSContent';

interface ContentPreviewProps {
  open: boolean;
  onClose: () => void;
  content: any;
  contentType: 'blog-post' | 'page-section' | 'media-file';
  onEdit?: () => void;
  onPublish?: () => void;
  onSchedule?: () => void;
}

const ContentPreview: React.FC<ContentPreviewProps> = ({
  open,
  onClose,
  content,
  contentType,
  onEdit,
  onPublish,
  onSchedule
}) => {
  const { language } = useLanguage();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [previewLanguage, setPreviewLanguage] = useState<'en' | 'kn' | 'hi'>('en');

  const getLocalizedText = (textObj: any) => {
    if (typeof textObj === 'string') return textObj;
    return textObj?.[previewLanguage] || textObj?.en || '';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(previewLanguage === 'kn' ? 'kn-IN' : previewLanguage === 'hi' ? 'hi-IN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderBlogPostPreview = (post: BlogPost) => (
    <Box>
      {/* Language Selector */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
        <Tabs
          value={previewLanguage}
          onChange={(_, newValue) => setPreviewLanguage(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            label="English"
            value="en"
            icon={<LanguageIcon />}
            iconPosition="start"
          />
          <Tab
            label="ಕನ್ನಡ"
            value="kn"
            icon={<LanguageIcon />}
            iconPosition="start"
          />
          <Tab
            label="हिंदी"
            value="hi"
            icon={<LanguageIcon />}
            iconPosition="start"
          />
        </Tabs>
      </Box>

      {/* Blog Post Preview */}
      <Card elevation={0} sx={{ border: '1px solid', borderColor: 'divider' }}>
        {/* Featured Image */}
        {post.image && (
          <CardMedia
            component="img"
            height={isMobile ? 200 : 300}
            image={post.image}
            alt={getLocalizedText(post.title)}
            sx={{ objectFit: 'cover' }}
          />
        )}

        <CardContent sx={{ p: { xs: 2, md: 4 } }}>
          {/* Meta Information */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
            <Chip
              label={getLocalizedText(post.category)}
              color="primary"
              size="small"
            />
            {post.featured && (
              <Chip
                label={previewLanguage === 'en' ? 'Featured' :
                       previewLanguage === 'kn' ? 'ವೈಶಿಷ್ಟ್ಯ' :
                       'विशेष'}
                color="secondary"
                size="small"
              />
            )}
            {post.tags?.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                variant="outlined"
                size="small"
              />
            ))}
          </Box>

          {/* Title */}
          <Typography
            variant={isMobile ? 'h4' : 'h3'}
            component="h1"
            sx={{
              fontWeight: 700,
              mb: 3,
              lineHeight: 1.2,
              color: 'text.primary'
            }}
          >
            {getLocalizedText(post.title)}
          </Typography>

          {/* Author and Date */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              flexWrap: 'wrap',
              gap: 2,
              mb: 3
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ width: 40, height: 40 }}>
                {getLocalizedText(post.author).charAt(0)}
              </Avatar>
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  {getLocalizedText(post.author)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formatDate(post.date)} • {getLocalizedText(post.readTime)}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton size="small" color="primary">
                <ShareIcon />
              </IconButton>
              <IconButton size="small" color="primary">
                <BookmarkIcon />
              </IconButton>
            </Box>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Excerpt */}
          <Typography
            variant="h6"
            sx={{
              fontStyle: 'italic',
              color: 'text.secondary',
              mb: 3,
              lineHeight: 1.6
            }}
          >
            {getLocalizedText(post.excerpt)}
          </Typography>

          {/* Content */}
          <Typography
            variant="body1"
            sx={{
              lineHeight: 1.8,
              fontSize: '1.1rem',
              color: 'text.primary',
              '& p': { mb: 2 },
              '& h1, & h2, & h3, & h4, & h5, & h6': {
                mt: 3,
                mb: 2,
                fontWeight: 600
              },
              '& ul, & ol': {
                pl: 3,
                mb: 2
              },
              '& blockquote': {
                borderLeft: 4,
                borderColor: 'primary.main',
                pl: 2,
                ml: 0,
                fontStyle: 'italic',
                color: 'text.secondary'
              }
            }}
          >
            {post.content ? getLocalizedText(post.content) : getLocalizedText(post.excerpt)}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );

  const renderPageSectionPreview = (section: any) => (
    <Box>
      <Alert severity="info" sx={{ mb: 3 }}>
        This is a preview of how this section will appear on the website.
      </Alert>
      
      <Card elevation={0} sx={{ border: '1px solid', borderColor: 'divider' }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom>
            {section.name || 'Section Preview'}
          </Typography>
          
          {section.type === 'text' && (
            <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
              {getLocalizedText(section.content)}
            </Typography>
          )}
          
          {section.type === 'image' && section.content && (
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={section.content}
                alt={section.alt || 'Section image'}
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  borderRadius: theme.shape.borderRadius
                }}
              />
            </Box>
          )}
          
          {section.type === 'list' && Array.isArray(section.content) && (
            <Box>
              {section.content.map((item: any, index: number) => (
                <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {getLocalizedText(item.title)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {getLocalizedText(item.description)}
                    </Typography>
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const renderMediaFilePreview = (file: any) => (
    <Box>
      <Alert severity="info" sx={{ mb: 3 }}>
        Media file preview - this shows how the file will be displayed.
      </Alert>
      
      <Card elevation={0} sx={{ border: '1px solid', borderColor: 'divider' }}>
        {file.type === 'image' && (
          <CardMedia
            component="img"
            image={file.url}
            alt={file.alt || file.name}
            sx={{
              maxHeight: 400,
              objectFit: 'contain',
              bgcolor: 'grey.100'
            }}
          />
        )}
        
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {file.name}
          </Typography>
          
          {file.description && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {file.description}
            </Typography>
          )}
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
            <Chip label={`Type: ${file.type}`} size="small" />
            <Chip label={`Size: ${formatFileSize(file.size)}`} size="small" />
            {file.tags?.map((tag: string, index: number) => (
              <Chip key={index} label={tag} variant="outlined" size="small" />
            ))}
          </Box>
          
          {file.alt && (
            <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
              Alt text: {file.alt}
            </Typography>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderPreviewContent = () => {
    switch (contentType) {
      case 'blog-post':
        return renderBlogPostPreview(content);
      case 'page-section':
        return renderPageSectionPreview(content);
      case 'media-file':
        return renderMediaFilePreview(content);
      default:
        return (
          <Alert severity="warning">
            Preview not available for this content type.
          </Alert>
        );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          height: '90vh',
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PreviewIcon />
          <Typography variant="h6">
            {language === 'en' ? 'Content Preview' :
             language === 'kn' ? 'ವಿಷಯ ಪೂರ್ವವೀಕ್ಷಣೆ' :
             'सामग्री पूर्वावलोकन'}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
          {renderPreviewContent()}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button onClick={onClose} color="inherit">
          {language === 'en' ? 'Close' :
           language === 'kn' ? 'ಮುಚ್ಚಿ' :
           'बंद करें'}
        </Button>
        
        {onEdit && (
          <Button onClick={onEdit} startIcon={<EditIcon />}>
            {language === 'en' ? 'Edit' :
             language === 'kn' ? 'ಸಂಪಾದಿಸಿ' :
             'संपादित करें'}
          </Button>
        )}
        
        {onSchedule && (
          <Button onClick={onSchedule} startIcon={<ScheduleIcon />} color="info">
            {language === 'en' ? 'Schedule' :
             language === 'kn' ? 'ವೇಳಾಪಟ್ಟಿ' :
             'शेड्यूल करें'}
          </Button>
        )}
        
        {onPublish && (
          <Button onClick={onPublish} startIcon={<PublishIcon />} variant="contained">
            {language === 'en' ? 'Publish' :
             language === 'kn' ? 'ಪ್ರಕಟಿಸಿ' :
             'प्रकाशित करें'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default ContentPreview;
