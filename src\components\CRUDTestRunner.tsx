import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  ExpandMore as ExpandMoreIcon,
  Assessment as ReportIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';
import { crudTester, testSuites, TestResult, TestSuite } from '../utils/crudTesting';

const CRUDTestRunner: React.FC = () => {
  const { language } = useLanguage();
  const [running, setRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false);

  const getLocalizedText = (key: string) => {
    const translations = {
      en: {
        title: 'CRUD Operations Test Runner',
        description: 'Comprehensive testing of all Create, Read, Update, Delete operations across content types',
        runAllTests: 'Run All Tests',
        runSuiteTests: 'Run Suite Tests',
        testResults: 'Test Results',
        testSummary: 'Test Summary',
        totalTests: 'Total Tests',
        passed: 'Passed',
        failed: 'Failed',
        successRate: 'Success Rate',
        duration: 'Duration',
        running: 'Running Tests...',
        completed: 'Tests Completed',
        noResults: 'No test results available. Run tests to see results.',
        viewReport: 'View Detailed Report'
      },
      kn: {
        title: 'CRUD ಕಾರ್ಯಾಚರಣೆಗಳ ಪರೀಕ್ಷಾ ರನ್ನರ್',
        description: 'ವಿಷಯ ಪ್ರಕಾರಗಳಾದ್ಯಂತ ಎಲ್ಲಾ ರಚಿಸು, ಓದು, ನವೀಕರಿಸು, ಅಳಿಸು ಕಾರ್ಯಾಚರಣೆಗಳ ಸಮಗ್ರ ಪರೀಕ್ಷೆ',
        runAllTests: 'ಎಲ್ಲಾ ಪರೀಕ್ಷೆಗಳನ್ನು ಚಲಾಯಿಸಿ',
        runSuiteTests: 'ಸೂಟ್ ಪರೀಕ್ಷೆಗಳನ್ನು ಚಲಾಯಿಸಿ',
        testResults: 'ಪರೀಕ್ಷಾ ಫಲಿತಾಂಶಗಳು',
        testSummary: 'ಪರೀಕ್ಷಾ ಸಾರಾಂಶ',
        totalTests: 'ಒಟ್ಟು ಪರೀಕ್ಷೆಗಳು',
        passed: 'ಉತ್ತೀರ್ಣ',
        failed: 'ವಿಫಲ',
        successRate: 'ಯಶಸ್ಸಿನ ದರ',
        duration: 'ಅವಧಿ',
        running: 'ಪರೀಕ್ಷೆಗಳನ್ನು ಚಲಾಯಿಸಲಾಗುತ್ತಿದೆ...',
        completed: 'ಪರೀಕ್ಷೆಗಳು ಪೂರ್ಣಗೊಂಡಿವೆ',
        noResults: 'ಯಾವುದೇ ಪರೀಕ್ಷಾ ಫಲಿತಾಂಶಗಳು ಲಭ್ಯವಿಲ್ಲ. ಫಲಿತಾಂಶಗಳನ್ನು ನೋಡಲು ಪರೀಕ್ಷೆಗಳನ್ನು ಚಲಾಯಿಸಿ.',
        viewReport: 'ವಿವರವಾದ ವರದಿಯನ್ನು ವೀಕ್ಷಿಸಿ'
      },
      hi: {
        title: 'CRUD ऑपरेशन टेस्ट रनर',
        description: 'सामग्री प्रकारों में सभी बनाएं, पढ़ें, अपडेट करें, हटाएं ऑपरेशन का व्यापक परीक्षण',
        runAllTests: 'सभी टेस्ट चलाएं',
        runSuiteTests: 'सूट टेस्ट चलाएं',
        testResults: 'टेस्ट परिणाम',
        testSummary: 'टेस्ट सारांश',
        totalTests: 'कुल टेस्ट',
        passed: 'पास',
        failed: 'फेल',
        successRate: 'सफलता दर',
        duration: 'अवधि',
        running: 'टेस्ट चल रहे हैं...',
        completed: 'टेस्ट पूर्ण',
        noResults: 'कोई टेस्ट परिणाम उपलब्ध नहीं। परिणाम देखने के लिए टेस्ट चलाएं।',
        viewReport: 'विस्तृत रिपोर्ट देखें'
      }
    };
    return translations[language as keyof typeof translations]?.[key as keyof typeof translations.en] || key;
  };

  const runAllTests = async () => {
    setRunning(true);
    setResults([]);
    setProgress(0);
    setCurrentTest('');

    try {
      const totalTests = testSuites.reduce((sum, suite) => sum + suite.tests.length, 0);
      let completedTests = 0;

      for (const testSuite of testSuites) {
        for (const testCase of testSuite.tests) {
          setCurrentTest(`${testSuite.name}: ${testCase.name}`);
          
          const result = await crudTester.runTest(testCase);
          setResults(prev => [...prev, result]);
          
          completedTests++;
          setProgress((completedTests / totalTests) * 100);
          
          // Small delay to show progress
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
    } catch (error) {
      console.error('Test execution failed:', error);
    } finally {
      setRunning(false);
      setCurrentTest('');
    }
  };

  const runSuiteTests = async (testSuite: TestSuite) => {
    setRunning(true);
    setResults([]);
    setProgress(0);
    setCurrentTest('');

    try {
      const totalTests = testSuite.tests.length;
      let completedTests = 0;

      for (const testCase of testSuite.tests) {
        setCurrentTest(`${testSuite.name}: ${testCase.name}`);
        
        const result = await crudTester.runTest(testCase);
        setResults(prev => [...prev, result]);
        
        completedTests++;
        setProgress((completedTests / totalTests) * 100);
        
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } catch (error) {
      console.error('Suite test execution failed:', error);
    } finally {
      setRunning(false);
      setCurrentTest('');
    }
  };

  const getTestIcon = (success: boolean) => {
    return success ? <SuccessIcon color="success" /> : <ErrorIcon color="error" />;
  };

  const getTestStats = () => {
    const total = results.length;
    const passed = results.filter(r => r.success).length;
    const failed = total - passed;
    const successRate = total > 0 ? (passed / total) * 100 : 0;
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

    return { total, passed, failed, successRate, totalDuration };
  };

  const stats = getTestStats();

  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAccordion(isExpanded ? panel : false);
  };

  const groupResultsByContentType = () => {
    const grouped: Record<string, TestResult[]> = {};
    results.forEach(result => {
      if (!grouped[result.contentType]) {
        grouped[result.contentType] = [];
      }
      grouped[result.contentType].push(result);
    });
    return grouped;
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {getLocalizedText('title')}
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        {getLocalizedText('description')}
      </Typography>

      {/* Control Panel */}
      <Card sx={{ mb: 4 }}>
        <CardHeader title="Test Controls" />
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<PlayIcon />}
              onClick={runAllTests}
              disabled={running}
            >
              {getLocalizedText('runAllTests')}
            </Button>
            
            {testSuites.map((suite, index) => (
              <Button
                key={index}
                variant="outlined"
                onClick={() => runSuiteTests(suite)}
                disabled={running}
              >
                {suite.name}
              </Button>
            ))}
            
            <Button
              startIcon={<RefreshIcon />}
              onClick={() => setResults([])}
              disabled={running}
            >
              Clear Results
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Progress */}
      {running && (
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {getLocalizedText('running')}
            </Typography>
            <LinearProgress variant="determinate" value={progress} sx={{ mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              {currentTest}
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Test Summary */}
      {results.length > 0 && (
        <Card sx={{ mb: 4 }}>
          <CardHeader title={getLocalizedText('testSummary')} />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={6} sm={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {stats.total}
                  </Typography>
                  <Typography variant="body2">
                    {getLocalizedText('totalTests')}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="success.main">
                    {stats.passed}
                  </Typography>
                  <Typography variant="body2">
                    {getLocalizedText('passed')}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="error.main">
                    {stats.failed}
                  </Typography>
                  <Typography variant="body2">
                    {getLocalizedText('failed')}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="info.main">
                    {stats.successRate.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2">
                    {getLocalizedText('successRate')}
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {getLocalizedText('duration')}: {stats.totalDuration}ms
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Test Results */}
      {results.length > 0 ? (
        <Card>
          <CardHeader title={getLocalizedText('testResults')} />
          <CardContent>
            {Object.entries(groupResultsByContentType()).map(([contentType, typeResults]) => (
              <Accordion
                key={contentType}
                expanded={expandedAccordion === contentType}
                onChange={handleAccordionChange(contentType)}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    <Typography variant="h6">
                      {contentType} Tests
                    </Typography>
                    <Chip
                      label={`${typeResults.filter(r => r.success).length}/${typeResults.length} passed`}
                      color={typeResults.every(r => r.success) ? 'success' : 'error'}
                      size="small"
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <List>
                    {typeResults.map((result, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          {getTestIcon(result.success)}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body1">
                                {result.operation.toUpperCase()} {result.contentType}
                              </Typography>
                              <Chip
                                label={`${result.duration}ms`}
                                size="small"
                                variant="outlined"
                              />
                            </Box>
                          }
                          secondary={result.message}
                        />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}
          </CardContent>
        </Card>
      ) : (
        <Alert severity="info">
          {getLocalizedText('noResults')}
        </Alert>
      )}
    </Container>
  );
};

export default CRUDTestRunner;
