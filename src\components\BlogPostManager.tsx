import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Chip,
  Grid,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Visibility as PreviewIcon
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';
import { useBlogContent, BlogPost } from '../hooks/useCMSContent';
import ContentPreview from './ContentPreview';

interface BlogPostFormData {
  title: { en: string; kn: string; hi: string };
  excerpt: { en: string; kn: string; hi: string };
  content: { en: string; kn: string; hi: string };
  category: string;
  author: { en: string; kn: string; hi: string };
  date: string;
  readTime: { en: string; kn: string; hi: string };
  image: string;
  featured: boolean;
  published: boolean;
  tags: string[];
  seoTitle?: string;
  seoDescription?: string;
}

const BlogPostManager: React.FC = () => {
  const { language } = useLanguage();
  const { blogContent, loading } = useBlogContent();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  const [currentTab, setCurrentTab] = useState(0);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewPost, setPreviewPost] = useState<BlogPost | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  
  const [formData, setFormData] = useState<BlogPostFormData>({
    title: { en: '', kn: '', hi: '' },
    excerpt: { en: '', kn: '', hi: '' },
    content: { en: '', kn: '', hi: '' },
    category: 'general',
    author: { en: '', kn: '', hi: '' },
    date: new Date().toISOString().split('T')[0],
    readTime: { en: '5 min read', kn: '5 ನಿಮಿಷ ಓದು', hi: '5 मिनट पढ़ें' },
    image: '',
    featured: false,
    published: true,
    tags: [],
    seoTitle: '',
    seoDescription: ''
  });

  const categories = [
    { value: 'sustainable', label: { en: 'Sustainable Farming', kn: 'ಸುಸ್ಥಿರ ಕೃಷಿ', hi: 'स्थायी खेती' } },
    { value: 'technology', label: { en: 'Technology', kn: 'ತಂತ್ರಜ್ಞಾನ', hi: 'तकनीक' } },
    { value: 'organic', label: { en: 'Organic Farming', kn: 'ಸಾವಯವ ಕೃಷಿ', hi: 'जैविक खेती' } },
    { value: 'water', label: { en: 'Water Management', kn: 'ನೀರಿನ ನಿರ್ವಹಣೆ', hi: 'जल प्रबंधन' } },
    { value: 'research', label: { en: 'Research', kn: 'ಸಂಶೋಧನೆ', hi: 'अनुसंधान' } },
    { value: 'general', label: { en: 'General', kn: 'ಸಾಮಾನ್ಯ', hi: 'सामान्य' } }
  ];

  useEffect(() => {
    if (blogContent?.posts) {
      setPosts(blogContent.posts);
    }
  }, [blogContent]);

  const getLocalizedText = (textObj: any) => {
    if (typeof textObj === 'string') return textObj;
    return textObj?.[language as keyof typeof textObj] || textObj?.en || '';
  };

  const handleOpenDialog = (post?: BlogPost) => {
    if (post) {
      setEditingPost(post);
      setFormData({
        title: post.title,
        excerpt: post.excerpt,
        content: post.content || post.excerpt,
        category: getCategoryValue(post.category),
        author: post.author,
        date: post.date,
        readTime: post.readTime,
        image: post.image,
        featured: post.featured || false,
        published: true,
        tags: post.tags || [],
        seoTitle: '',
        seoDescription: ''
      });
    } else {
      setEditingPost(null);
      setFormData({
        title: { en: '', kn: '', hi: '' },
        excerpt: { en: '', kn: '', hi: '' },
        content: { en: '', kn: '', hi: '' },
        category: 'general',
        author: { en: '', kn: '', hi: '' },
        date: new Date().toISOString().split('T')[0],
        readTime: { en: '5 min read', kn: '5 ನಿಮಿಷ ಓದು', hi: '5 मिनट पढ़ें' },
        image: '',
        featured: false,
        published: true,
        tags: [],
        seoTitle: '',
        seoDescription: ''
      });
    }
    setDialogOpen(true);
    setCurrentTab(0);
  };

  const getCategoryValue = (categoryObj: any): string => {
    if (typeof categoryObj === 'string') return categoryObj;
    const categoryText = getLocalizedText(categoryObj).toLowerCase();
    const category = categories.find(cat => 
      getLocalizedText(cat.label).toLowerCase() === categoryText
    );
    return category?.value || 'general';
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingPost(null);
    setCurrentTab(0);
  };

  const handleSave = async () => {
    try {
      // Validate required fields
      if (!formData.title.en || !formData.excerpt.en || !formData.content.en) {
        setSnackbar({
          open: true,
          message: 'Please fill in all required fields (at least English version)',
          severity: 'error'
        });
        return;
      }

      const categoryObj = categories.find(cat => cat.value === formData.category);
      const newPost: BlogPost = {
        id: editingPost?.id || Date.now(),
        title: formData.title,
        excerpt: formData.excerpt,
        content: formData.content,
        category: categoryObj?.label || { en: 'General', kn: 'ಸಾಮಾನ್ಯ', hi: 'सामान्य' },
        author: formData.author,
        date: formData.date,
        readTime: formData.readTime,
        image: formData.image,
        featured: formData.featured,
        tags: formData.tags
      };

      if (editingPost) {
        // Update existing post
        setPosts(prev => prev.map(post => post.id === editingPost.id ? newPost : post));
        setSnackbar({
          open: true,
          message: 'Blog post updated successfully!',
          severity: 'success'
        });
      } else {
        // Add new post
        setPosts(prev => [newPost, ...prev]);
        setSnackbar({
          open: true,
          message: 'Blog post created successfully!',
          severity: 'success'
        });
      }

      handleCloseDialog();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to save blog post. Please try again.',
        severity: 'error'
      });
    }
  };

  const handleDelete = async (postId: number) => {
    if (window.confirm('Are you sure you want to delete this blog post?')) {
      try {
        setPosts(prev => prev.filter(post => post.id !== postId));
        setSnackbar({
          open: true,
          message: 'Blog post deleted successfully!',
          severity: 'success'
        });
      } catch (error) {
        setSnackbar({
          open: true,
          message: 'Failed to delete blog post. Please try again.',
          severity: 'error'
        });
      }
    }
  };

  const handleTagAdd = (tag: string) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const handleTagRemove = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handlePreview = (post: BlogPost) => {
    setPreviewPost(post);
    setPreviewOpen(true);
  };

  if (loading) {
    return (
      <Container>
        <Typography>Loading blog posts...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          {language === 'en' ? 'Blog Post Management' :
           language === 'kn' ? 'ಬ್ಲಾಗ್ ಪೋಸ್ಟ್ ನಿರ್ವಹಣೆ' :
           'ब्लॉग पोस्ट प्रबंधन'}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          {language === 'en' ? 'Add New Post' :
           language === 'kn' ? 'ಹೊಸ ಪೋಸ್ಟ್ ಸೇರಿಸಿ' :
           'नई पोस्ट जोड़ें'}
        </Button>
      </Box>

      <Grid container spacing={3}>
        {posts.map((post) => (
          <Grid item xs={12} md={6} lg={4} key={post.id}>
            <Card>
              <Box
                component="img"
                src={post.image}
                alt={getLocalizedText(post.title)}
                sx={{
                  width: '100%',
                  height: 200,
                  objectFit: 'cover'
                }}
              />
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {getLocalizedText(post.title)}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {getLocalizedText(post.excerpt)}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                  <Chip
                    label={getLocalizedText(post.category)}
                    size="small"
                    color="primary"
                  />
                  {post.featured && (
                    <Chip
                      label={language === 'en' ? 'Featured' :
                             language === 'kn' ? 'ವೈಶಿಷ್ಟ್ಯ' :
                             'विशेष'}
                      size="small"
                      color="secondary"
                    />
                  )}
                </Box>
                <Typography variant="caption" color="text.secondary">
                  {getLocalizedText(post.author)} • {post.date}
                </Typography>
              </CardContent>
              <CardActions>
                <IconButton
                  size="small"
                  onClick={() => handleOpenDialog(post)}
                  color="primary"
                >
                  <EditIcon />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handleDelete(post.id)}
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
                <IconButton
                  size="small"
                  color="info"
                  onClick={() => handlePreview(post)}
                >
                  <PreviewIcon />
                </IconButton>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Blog Post Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle>
          {editingPost
            ? (language === 'en' ? 'Edit Blog Post' :
               language === 'kn' ? 'ಬ್ಲಾಗ್ ಪೋಸ್ಟ್ ಸಂಪಾದಿಸಿ' :
               'ब्लॉग पोस्ट संपादित करें')
            : (language === 'en' ? 'Create New Blog Post' :
               language === 'kn' ? 'ಹೊಸ ಬ್ಲಾಗ್ ಪೋಸ್ಟ್ ರಚಿಸಿ' :
               'नई ब्लॉग पोस्ट बनाएं')
          }
        </DialogTitle>
        <DialogContent>
          <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
            <Tab label="English" />
            <Tab label="ಕನ್ನಡ" />
            <Tab label="हिंदी" />
            <Tab label="Settings" />
          </Tabs>

          <Box sx={{ mt: 2 }}>
            {/* English Content Tab */}
            {currentTab === 0 && (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <TextField
                  label="Title (English)"
                  value={formData.title.en}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    title: { ...prev.title, en: e.target.value }
                  }))}
                  fullWidth
                  required
                />
                <TextField
                  label="Excerpt (English)"
                  value={formData.excerpt.en}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    excerpt: { ...prev.excerpt, en: e.target.value }
                  }))}
                  fullWidth
                  multiline
                  rows={3}
                  required
                />
                <TextField
                  label="Content (English)"
                  value={formData.content.en}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    content: { ...prev.content, en: e.target.value }
                  }))}
                  fullWidth
                  multiline
                  rows={8}
                  required
                />
                <TextField
                  label="Author (English)"
                  value={formData.author.en}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    author: { ...prev.author, en: e.target.value }
                  }))}
                  fullWidth
                />
              </Box>
            )}

            {/* Kannada Content Tab */}
            {currentTab === 1 && (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <TextField
                  label="Title (ಕನ್ನಡ)"
                  value={formData.title.kn}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    title: { ...prev.title, kn: e.target.value }
                  }))}
                  fullWidth
                />
                <TextField
                  label="Excerpt (ಕನ್ನಡ)"
                  value={formData.excerpt.kn}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    excerpt: { ...prev.excerpt, kn: e.target.value }
                  }))}
                  fullWidth
                  multiline
                  rows={3}
                />
                <TextField
                  label="Content (ಕನ್ನಡ)"
                  value={formData.content.kn}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    content: { ...prev.content, kn: e.target.value }
                  }))}
                  fullWidth
                  multiline
                  rows={8}
                />
                <TextField
                  label="Author (ಕನ್ನಡ)"
                  value={formData.author.kn}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    author: { ...prev.author, kn: e.target.value }
                  }))}
                  fullWidth
                />
              </Box>
            )}

            {/* Hindi Content Tab */}
            {currentTab === 2 && (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <TextField
                  label="Title (हिंदी)"
                  value={formData.title.hi}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    title: { ...prev.title, hi: e.target.value }
                  }))}
                  fullWidth
                />
                <TextField
                  label="Excerpt (हिंदी)"
                  value={formData.excerpt.hi}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    excerpt: { ...prev.excerpt, hi: e.target.value }
                  }))}
                  fullWidth
                  multiline
                  rows={3}
                />
                <TextField
                  label="Content (हिंदी)"
                  value={formData.content.hi}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    content: { ...prev.content, hi: e.target.value }
                  }))}
                  fullWidth
                  multiline
                  rows={8}
                />
                <TextField
                  label="Author (हिंदी)"
                  value={formData.author.hi}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    author: { ...prev.author, hi: e.target.value }
                  }))}
                  fullWidth
                />
              </Box>
            )}

            {/* Settings Tab */}
            {currentTab === 3 && (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      category: e.target.value
                    }))}
                  >
                    {categories.map((category) => (
                      <MenuItem key={category.value} value={category.value}>
                        {getLocalizedText(category.label)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  label="Publish Date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    date: e.target.value
                  }))}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />

                <TextField
                  label="Featured Image URL"
                  value={formData.image}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    image: e.target.value
                  }))}
                  fullWidth
                />

                <Box sx={{ display: 'flex', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.featured}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          featured: e.target.checked
                        }))}
                      />
                    }
                    label="Featured Post"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.published}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          published: e.target.checked
                        }))}
                      />
                    }
                    label="Published"
                  />
                </Box>

                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Tags
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {formData.tags.map((tag) => (
                      <Chip
                        key={tag}
                        label={tag}
                        onDelete={() => handleTagRemove(tag)}
                        size="small"
                      />
                    ))}
                  </Box>
                  <TextField
                    label="Add Tag"
                    size="small"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const target = e.target as HTMLInputElement;
                        handleTagAdd(target.value);
                        target.value = '';
                      }
                    }}
                    helperText="Press Enter to add tag"
                  />
                </Box>

                <Divider />

                <TextField
                  label="SEO Title"
                  value={formData.seoTitle}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    seoTitle: e.target.value
                  }))}
                  fullWidth
                />

                <TextField
                  label="SEO Description"
                  value={formData.seoDescription}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    seoDescription: e.target.value
                  }))}
                  fullWidth
                  multiline
                  rows={3}
                />
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<CancelIcon />}>
            {language === 'en' ? 'Cancel' :
             language === 'kn' ? 'ರದ್ದುಮಾಡಿ' :
             'रद्द करें'}
          </Button>
          <Button onClick={handleSave} variant="contained" startIcon={<SaveIcon />}>
            {language === 'en' ? 'Save' :
             language === 'kn' ? 'ಉಳಿಸಿ' :
             'सेव करें'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Content Preview */}
      {previewPost && (
        <ContentPreview
          open={previewOpen}
          onClose={() => setPreviewOpen(false)}
          content={previewPost}
          contentType="blog-post"
          onEdit={() => {
            setPreviewOpen(false);
            handleOpenDialog(previewPost);
          }}
          onPublish={() => {
            // Handle publish logic
            setSnackbar({
              open: true,
              message: 'Blog post published successfully!',
              severity: 'success'
            });
            setPreviewOpen(false);
          }}
        />
      )}
    </Container>
  );
};

export default BlogPostManager;
