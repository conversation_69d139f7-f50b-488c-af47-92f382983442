backend:
  name: git-gateway
  branch: main # Branch to update (optional; defaults to master)
  repo: aimaniahub/darvi-registration

# This line should match your site URL
site_url: https://darvigroup.in

# Media files will be stored in the repo under public/darvi-images
media_folder: "public/darvi-images"
# The src attribute for uploaded media will begin with /darvi-images
public_folder: "/darvi-images"

# Enable media library for better image management
media_library:
  name: cloudinary
  config:
    cloud_name: "darvi-group"
    api_key: ${CLOUDINARY_API_KEY}

# Enable image optimization and processing
media_library_config:
  max_file_size: 10485760  # 10MB
  allowed_extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
  image_transformations:
    - name: "thumbnail"
      width: 300
      height: 200
      crop: "fill"
    - name: "medium"
      width: 800
      height: 600
      crop: "fit"
    - name: "large"
      width: 1200
      height: 800
      crop: "fit"

# Enable preview functionality
show_preview_links: true
display_url: https://darvigroup.in

# Editor configuration
editor:
  preview: true

# Enable local backend for development
local_backend: true

# Slug configuration
slug:
  encoding: "ascii"
  clean_accents: true

# Custom widget configurations for better image handling
widgets:
  - name: "enhanced_image"
    label: "Enhanced Image"
    widget: "image"
    allow_multiple: false
    media_library:
      config:
        multiple: false
        max_file_size: 10485760
        allowed_extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp']
        transformations:
          - name: "thumbnail"
            width: 300
            height: 200
          - name: "medium"
            width: 800
            height: 600
          - name: "large"
            width: 1200
            height: 800

collections:
  # Media Management Collection
  - name: "media"
    label: "Media Library"
    folder: "public/darvi-images"
    create: true
    slug: "{{slug}}"
    media_folder: ""
    public_folder: "/darvi-images"
    fields:
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Description", name: "description", widget: "text", required: false }
      - { label: "Alt Text", name: "alt", widget: "string" }
      - { label: "Image", name: "image", widget: "image" }
      - { label: "Category", name: "category", widget: "select", options: ["hero", "gallery", "team", "products", "blog", "general"] }
      - { label: "Tags", name: "tags", widget: "list", required: false }

  - name: "home"
    label: "Home Page"
    files:
      - file: "src/content/home/<USER>"
        label: "Hero Section"
        name: "hero"
        fields:
          - label: "Slides"
            name: "slides"
            widget: "list"
            fields:
              - { label: "Image", name: "image", widget: "image", allow_multiple: false }
              - label: "Title"
                name: "title"
                widget: "object"
                fields:
                  - { label: "English", name: "en", widget: "string" }
                  - { label: "Kannada", name: "kn", widget: "string" }
                  - { label: "Hindi", name: "hi", widget: "string" }
              - label: "Description"
                name: "description"
                widget: "object"
                fields:
                  - { label: "English", name: "en", widget: "string" }
                  - { label: "Kannada", name: "kn", widget: "string" }
                  - { label: "Hindi", name: "hi", widget: "string" }
              - { label: "URL", name: "url", widget: "string" }

      - file: "src/content/home/<USER>"
        label: "Values Section"
        name: "values"
        fields:
          - label: "Values"
            name: "values"
            widget: "list"
            fields:
              - { label: "Icon", name: "icon", widget: "select", options: ["EmojiNature", "Groups", "Lightbulb", "TrendingUp"] }
              - label: "Title"
                name: "title"
                widget: "object"
                fields:
                  - { label: "English", name: "en", widget: "string" }
                  - { label: "Kannada", name: "kn", widget: "string" }
                  - { label: "Hindi", name: "hi", widget: "string" }
              - label: "Description"
                name: "description"
                widget: "object"
                fields:
                  - { label: "English", name: "en", widget: "string" }
                  - { label: "Kannada", name: "kn", widget: "string" }
                  - { label: "Hindi", name: "hi", widget: "string" }

      - file: "src/content/home/<USER>"
        label: "Team Section"
        name: "team"
        fields:
          - label: "Section Title"
            name: "sectionTitle"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string", default: "Our Team" }
              - { label: "Kannada", name: "kn", widget: "string", default: "ನಮ್ಮ ತಂಡ" }
              - { label: "Hindi", name: "hi", widget: "string", default: "हमारी टीम" }

          - label: "Section Subtitle"
            name: "sectionSubtitle"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string", default: "Meet the passionate experts behind Darvi Group's success" }
              - { label: "Kannada", name: "kn", widget: "string", default: "ದಾರ್ವಿ ಗ್ರೂಪ್‌ನ ಯಶಸ್ಸಿನ ಹಿಂದಿನ ಉತ್ಸಾಹಿ ತಜ್ಞರನ್ನು ಭೇಟಿ ಮಾಡಿ" }
              - { label: "Hindi", name: "hi", widget: "string", default: "दारवी ग्रुप की सफलता के पीछे जुनूनी विशेषज्ञों से मिलें" }

          - label: "View All Link Text"
            name: "viewAllLinkText"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string", default: "Meet our entire team →" }
              - { label: "Kannada", name: "kn", widget: "string", default: "ನಮ್ಮ ಇಡೀ ತಂಡವನ್ನು ಭೇಟಿ ಮಾಡಿ →" }
              - { label: "Hindi", name: "hi", widget: "string", default: "हमारी पूरी टीम से मिलें →" }

          - label: "Team Members"
            name: "teamMembers"
            widget: "list"
            fields:
              - { label: "Name", name: "name", widget: "string" }
              - { label: "Position", name: "position", widget: "string" }
              - { label: "Image", name: "image", widget: "image", allow_multiple: false }
              - { label: "Education", name: "education", widget: "string" }
              - { label: "Specialization", name: "specialization", widget: "string", required: false }
              - { label: "Experience", name: "experience", widget: "string", required: false }
              - { label: "Current Project", name: "currentProject", widget: "string", required: false }
              - { label: "Short Description", name: "shortDescription", widget: "text" }
              - { label: "Full Description", name: "fullDescription", widget: "text", required: false }
              - { label: "Background", name: "background", widget: "text", required: false }
              - { label: "Mission", name: "mission", widget: "text", required: false }

      - file: "src/content/home/<USER>"
        label: "IoT Features Section"
        name: "iot-features"
        fields:
          - label: "Title"
            name: "title"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }
          - label: "Subtitle"
            name: "subtitle"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }
          - label: "Link Text"
            name: "linkText"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }
          - label: "Features"
            name: "features"
            widget: "list"
            fields:
              - { label: "Icon", name: "icon", widget: "select", options: ["Router", "BarChart", "Nature", "Public"] }
              - label: "Title"
                name: "title"
                widget: "object"
                fields:
                  - { label: "English", name: "en", widget: "string" }
                  - { label: "Kannada", name: "kn", widget: "string" }
                  - { label: "Hindi", name: "hi", widget: "string" }
              - label: "Description"
                name: "description"
                widget: "object"
                fields:
                  - { label: "English", name: "en", widget: "string" }
                  - { label: "Kannada", name: "kn", widget: "string" }
                  - { label: "Hindi", name: "hi", widget: "string" }

      - file: "src/content/home/<USER>"
        label: "Contact Section"
        name: "contact"
        fields:
          - label: "Title"
            name: "title"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }
          - label: "Subtitle"
            name: "subtitle"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }
          - label: "Contact Info"
            name: "contactInfo"
            widget: "list"
            fields:
              - { label: "Icon", name: "icon", widget: "select", options: ["LocationOn", "Phone", "Email"] }
              - label: "Title"
                name: "title"
                widget: "object"
                fields:
                  - { label: "English", name: "en", widget: "string" }
                  - { label: "Kannada", name: "kn", widget: "string" }
                  - { label: "Hindi", name: "hi", widget: "string" }
              - { label: "Content", name: "content", widget: "string" }

      - file: "src/content/home/<USER>"
        label: "Gallery Section"
        name: "gallery"
        fields:
          - label: "Title"
            name: "title"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }
          - label: "Subtitle"
            name: "subtitle"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }
          - label: "Images"
            name: "images"
            widget: "list"
            fields:
              - { label: "Image", name: "src", widget: "image", allow_multiple: false }
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Fallback Image", name: "fallbackSrc", widget: "string", default: "/darvi-logo.png" }

      - file: "src/content/home/<USER>"
        label: "Testimonials Section"
        name: "testimonials"
        fields:
          - label: "Section Title"
            name: "sectionTitle"
            widget: "string"
          - label: "Section Subtitle"
            name: "sectionSubtitle"
            widget: "string"
          - label: "Testimonials"
            name: "testimonials"
            widget: "list"
            fields:
              - { label: "Name", name: "name", widget: "string" }
              - { label: "Role", name: "role", widget: "string" }
              - { label: "Testimonial", name: "testimonial", widget: "text" }
              - { label: "Rating", name: "rating", widget: "number", value_type: "int", min: 1, max: 5 }
              - { label: "Image", name: "image", widget: "image", allow_multiple: false }

      - file: "src/content/home/<USER>"
        label: "Home About Section"
        name: "home_about_section"
        fields:
          - label: "Title"
            name: "title"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }
          - label: "Text"
            name: "text"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "text" }
              - { label: "Kannada", name: "kn", widget: "text" }
              - { label: "Hindi", name: "hi", widget: "text" }
          - label: "Image"
            name: "image"
            widget: "image"
            required: false
            allow_multiple: false
          - label: "Button Text"
            name: "buttonText"
            widget: "object"
            required: false
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }
          - label: "Button URL"
            name: "buttonUrl"
            widget: "string"
            required: false

  - name: "about"
    label: "About Page"
    files:
      - file: "src/content/about/content.json"
        label: "About Content"
        name: "content"
        fields:
          - label: "Hero"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Subtitle", name: "subtitle", widget: "string" }
          - label: "Business"
            name: "business"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Content", name: "content", widget: "list", field: { label: "Paragraph", name: "paragraph", widget: "text" } }
          - label: "Mission"
            name: "mission"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Content", name: "content", widget: "text" }
          - label: "Values"
            name: "values"
            widget: "list"
            fields:
              - { label: "Icon", name: "icon", widget: "select", options: ["EmojiNature", "Groups", "Lightbulb", "TrendingUp"] }
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Description", name: "description", widget: "text" }
          - label: "Journey"
            name: "journey"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Content", name: "content", widget: "list", field: { label: "Paragraph", name: "paragraph", widget: "text" } }
          - label: "Success Stories"
            name: "successStories"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - label: "Stories"
                name: "stories"
                widget: "list"
                fields:
                  - { label: "Title", name: "title", widget: "string" }
                  - { label: "Description", name: "description", widget: "text" }
          - label: "Contact Info"
            name: "contactInfo"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Address", name: "address", widget: "string" }
              - { label: "Phone", name: "phone", widget: "string" }
              - { label: "Email", name: "email", widget: "string" }
              - { label: "Working Hours", name: "workingHours", widget: "string" }

  - name: "contact"
    label: "Contact Page"
    files:
      - file: "src/content/contact/content.json"
        label: "Contact Content"
        name: "content"
        fields:
          - label: "Hero"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Subtitle", name: "subtitle", widget: "string" }
          - label: "Contact Info"
            name: "contactInfo"
            widget: "list"
            fields:
              - { label: "Icon", name: "icon", widget: "select", options: ["LocationOn", "Phone", "Email", "AccessTime"] }
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Content", name: "content", widget: "string" }
          - label: "Form"
            name: "form"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Name Placeholder", name: "namePlaceholder", widget: "string" }
              - { label: "Email Placeholder", name: "emailPlaceholder", widget: "string" }
              - { label: "Subject Placeholder", name: "subjectPlaceholder", widget: "string" }
              - { label: "Message Placeholder", name: "messagePlaceholder", widget: "string" }
              - { label: "Submit Button Text", name: "submitText", widget: "string" }
              - { label: "Success Message", name: "successMessage", widget: "string" }
              - { label: "Error Message", name: "errorMessage", widget: "string" }

  - name: "faq"
    label: "FAQ Page"
    files:
      - file: "src/content/faq/content.json"
        label: "FAQ Content"
        name: "content"
        fields:
          - label: "Hero"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Subtitle", name: "subtitle", widget: "string" }
          - label: "FAQs"
            name: "faqs"
            widget: "list"
            fields:
              - { label: "Question", name: "question", widget: "string" }
              - { label: "Answer", name: "answer", widget: "text" }

  - name: "iot"
    label: "IoT Management Page"
    files:
      - file: "src/content/iot/content.json"
        label: "IoT Content"
        name: "content"
        fields:
          - label: "Hero"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Subtitle", name: "subtitle", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Description", name: "description", widget: "object", fields: [
                  { label: "English", name: "en", widget: "text" },
                  { label: "Kannada", name: "kn", widget: "text" },
                  { label: "Hindi", name: "hi", widget: "text" }
                ]}
              - { label: "Button Text", name: "buttonText", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
          - label: "Features"
            name: "features"
            widget: "list"
            fields:
              - { label: "Icon", name: "icon", widget: "select", options: ["Router", "BarChart", "Nature", "Public"] }
              - { label: "Title", name: "title", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Description", name: "description", widget: "object", fields: [
                  { label: "English", name: "en", widget: "text" },
                  { label: "Kannada", name: "kn", widget: "text" },
                  { label: "Hindi", name: "hi", widget: "text" }
                ]}

  - name: "research"
    label: "Research Center Page"
    files:
      - file: "src/content/research/content.json"
        label: "Research Center Content"
        name: "content"
        fields:
          - label: "Hero Section"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Subtitle", name: "subtitle", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Background Image", name: "backgroundImage", widget: "image", required: false }
          
          - label: "Research Areas"
            name: "researchAreas"
            widget: "list"
            fields:
              - { label: "Name", name: "name", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Subtitle", name: "subtitle", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Description", name: "description", widget: "object", fields: [
                  { label: "English", name: "en", widget: "text" },
                  { label: "Kannada", name: "kn", widget: "text" },
                  { label: "Hindi", name: "hi", widget: "text" }
                ]}
              - { label: "Benefits", name: "benefits", widget: "list", field: { label: "Benefit", name: "benefit", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}}
              - { label: "Image", name: "image", widget: "image", required: false }
              - { label: "Icon", name: "icon", widget: "select", options: ["Forest", "Nature", "LocalFlorist", "Science"] }
          
          - label: "Statistics"
            name: "statistics"
            widget: "list"
            fields:
              - { label: "Number", name: "number", widget: "string" }
              - { label: "Label", name: "label", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Icon", name: "icon", widget: "select", options: ["Timeline", "TrendingUp", "Public", "Science"] }
          
          - label: "Methodologies"
            name: "methodologies"
            widget: "list"
            fields:
              - { label: "Title", name: "title", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Description", name: "description", widget: "object", fields: [
                  { label: "English", name: "en", widget: "text" },
                  { label: "Kannada", name: "kn", widget: "text" },
                  { label: "Hindi", name: "hi", widget: "text" }
                ]}
          
          - label: "Partnerships"
            name: "partnerships"
            widget: "list"
            fields:
              - { label: "Title", name: "title", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Description", name: "description", widget: "object", fields: [
                  { label: "English", name: "en", widget: "text" },
                  { label: "Kannada", name: "kn", widget: "text" },
                  { label: "Hindi", name: "hi", widget: "text" }
                ]}
              - { label: "Partner Count", name: "partnerCount", widget: "string" }
              - { label: "Icon", name: "icon", widget: "select", options: ["Agro", "Science", "Public"] }

  # Individual Blog Posts Collection
  - name: "blog-posts"
    label: "Blog Posts"
    folder: "src/content/blog/posts"
    create: true
    slug: "{{year}}-{{month}}-{{day}}-{{slug}}"
    fields:
      - { label: "Title", name: "title", widget: "object", fields: [
          { label: "English", name: "en", widget: "string" },
          { label: "Kannada", name: "kn", widget: "string" },
          { label: "Hindi", name: "hi", widget: "string" }
        ]}
      - { label: "Excerpt", name: "excerpt", widget: "object", fields: [
          { label: "English", name: "en", widget: "text" },
          { label: "Kannada", name: "kn", widget: "text" },
          { label: "Hindi", name: "hi", widget: "text" }
        ]}
      - { label: "Content", name: "content", widget: "object", fields: [
          { label: "English", name: "en", widget: "markdown" },
          { label: "Kannada", name: "kn", widget: "markdown" },
          { label: "Hindi", name: "hi", widget: "markdown" }
        ]}
      - { label: "Category", name: "category", widget: "select", options: [
          { label: "Sustainable Farming", value: "sustainable" },
          { label: "Technology", value: "technology" },
          { label: "Organic Farming", value: "organic" },
          { label: "Water Management", value: "water" },
          { label: "Research", value: "research" },
          { label: "General", value: "general" }
        ]}
      - { label: "Author", name: "author", widget: "object", fields: [
          { label: "English", name: "en", widget: "string" },
          { label: "Kannada", name: "kn", widget: "string" },
          { label: "Hindi", name: "hi", widget: "string" }
        ]}
      - { label: "Publish Date", name: "date", widget: "datetime", format: "YYYY-MM-DD" }
      - { label: "Read Time", name: "readTime", widget: "object", fields: [
          { label: "English", name: "en", widget: "string", default: "5 min read" },
          { label: "Kannada", name: "kn", widget: "string", default: "5 ನಿಮಿಷ ಓದು" },
          { label: "Hindi", name: "hi", widget: "string", default: "5 मिनट पढ़ें" }
        ]}
      - { label: "Featured Image", name: "image", widget: "image" }
      - { label: "Featured Post", name: "featured", widget: "boolean", default: false }
      - { label: "Published", name: "published", widget: "boolean", default: true }
      - { label: "Tags", name: "tags", widget: "list", field: { label: "Tag", name: "tag", widget: "string" } }
      - { label: "SEO Title", name: "seoTitle", widget: "string", required: false }
      - { label: "SEO Description", name: "seoDescription", widget: "text", required: false }

  - name: "blog"
    label: "Blog Page Settings"
    files:
      - file: "src/content/blog/content.json"
        label: "Blog Page Content"
        name: "content"
        fields:
          - label: "Hero Section"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Subtitle", name: "subtitle", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Background Image", name: "backgroundImage", widget: "image", required: false }
          - label: "Categories"
            name: "categories"
            widget: "list"
            fields:
              - { label: "Value", name: "value", widget: "string" }
              - { label: "Label", name: "label", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
          - label: "Posts Per Page"
            name: "postsPerPage"
            widget: "number"
            default: 9
          - label: "Enable Search"
            name: "enableSearch"
            widget: "boolean"
            default: true
          - label: "Enable Categories Filter"
            name: "enableCategoriesFilter"
            widget: "boolean"
            default: true

  - name: "form"
    label: "Consultation Form Page"
    files:
      - file: "src/content/form/content.json"
        label: "Form Content"
        name: "content"
        fields:
          - label: "Hero"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Subtitle", name: "subtitle", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
          - label: "Form Fields"
            name: "formFields"
            widget: "object"
            fields:
              - { label: "Personal Info Title", name: "personalInfoTitle", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Land Info Title", name: "landInfoTitle", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Submit Button Text", name: "submitText", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}

  - name: "layout"
    label: "Layout Components"
    files:
      - file: "src/content/layout/header.json"
        label: "Header/Navigation"
        name: "header"
        fields:
          - label: "Menu Items"
            name: "menuItems"
            widget: "list"
            fields:
              - { label: "Text (English)", name: "en", widget: "string" }
              - { label: "Text (Kannada)", name: "kn", widget: "string" }
              - { label: "Text (Hindi)", name: "hi", widget: "string" }
              - { label: "Path", name: "path", widget: "string" }
              - { label: "Icon", name: "icon", widget: "select", options: ["Home", "Info", "Business", "AttachMoney", "Router", "ContactPhone", "QuestionAnswer", "AppRegistration", "LocalFlorist"] }

          - label: "Language Selector"
            name: "languageSelector"
            widget: "object"
            fields:
              - { label: "Label (English)", name: "en", widget: "string", default: "Language" }
              - { label: "Label (Kannada)", name: "kn", widget: "string", default: "ಭಾಷೆ" }
              - { label: "Label (Hindi)", name: "hi", widget: "string", default: "भाषा" }
              - label: "Languages"
                name: "languages"
                widget: "list"
                fields:
                  - { label: "Code", name: "code", widget: "string" }
                  - { label: "Text", name: "text", widget: "string" }

      - file: "src/content/layout/footer.json"
        label: "Footer"
        name: "footer"
        fields:
          - label: "Company Description"
            name: "companyDescription"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "text" }
              - { label: "Kannada", name: "kn", widget: "text" }
              - { label: "Hindi", name: "hi", widget: "text" }

          - label: "Quick Links"
            name: "quickLinks"
            widget: "object"
            fields:
              - { label: "Title (English)", name: "titleEn", widget: "string" }
              - { label: "Title (Kannada)", name: "titleKn", widget: "string" }
              - { label: "Title (Hindi)", name: "titleHi", widget: "string" }
              - label: "Links"
                name: "links"
                widget: "list"
                fields:
                  - { label: "Text (English)", name: "en", widget: "string" }
                  - { label: "Text (Kannada)", name: "kn", widget: "string" }
                  - { label: "Text (Hindi)", name: "hi", widget: "string" }
                  - { label: "URL", name: "url", widget: "string" }

          - label: "Policies"
            name: "policies"
            widget: "object"
            fields:
              - { label: "Title (English)", name: "titleEn", widget: "string" }
              - { label: "Title (Kannada)", name: "titleKn", widget: "string" }
              - { label: "Title (Hindi)", name: "titleHi", widget: "string" }
              - label: "Links"
                name: "links"
                widget: "list"
                fields:
                  - { label: "Text (English)", name: "en", widget: "string" }
                  - { label: "Text (Kannada)", name: "kn", widget: "string" }
                  - { label: "Text (Hindi)", name: "hi", widget: "string" }
                  - { label: "URL", name: "url", widget: "string" }

          - label: "Social Media"
            name: "socialMedia"
            widget: "list"
            fields:
              - { label: "Platform", name: "platform", widget: "select", options: ["Facebook", "Twitter", "Instagram", "LinkedIn"] }
              - { label: "URL", name: "url", widget: "string" }

          - label: "Copyright"
            name: "copyright"
            widget: "object"
            fields:
              - { label: "English", name: "en", widget: "string" }
              - { label: "Kannada", name: "kn", widget: "string" }
              - { label: "Hindi", name: "hi", widget: "string" }

  - name: "services"
    label: "Services Page"
    files:
      - file: "src/content/services/content.json"
        label: "Services Content"
        name: "content"
        fields:
          - label: "Hero Section"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Subtitle", name: "subtitle", widget: "string" }

          - label: "Section Titles"
            name: "sections"
            widget: "object"
            fields:
              - { label: "Benefits Section Title", name: "benefitsTitle", widget: "string" }
              - { label: "Process Section Title", name: "processTitle", widget: "string" }
              - { label: "Contact Section Title", name: "contactTitle", widget: "string" }

          - label: "Labels"
            name: "labels"
            widget: "object"
            fields:
              - { label: "Included in Registration Label", name: "includedInRegistration", widget: "string" }

          - label: "Buttons"
            name: "buttons"
            widget: "object"
            fields:
              - { label: "Register Now Button", name: "registerNow", widget: "string" }
              - { label: "View Pricing Details Button", name: "viewPricingDetails", widget: "string" }

          - label: "Contact Labels"
            name: "contactLabels"
            widget: "object"
            fields:
              - { label: "Phone Label", name: "phone", widget: "string" }
              - { label: "Email Label", name: "email", widget: "string" }
              - { label: "Address Label", name: "address", widget: "string" }

          - label: "Working Hours Label"
            name: "workingHoursLabel"
            widget: "string"

          - label: "Pricing Section"
            name: "pricing"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Subtitle", name: "subtitle", widget: "string" }
              - { label: "Amount", name: "amount", widget: "number" }
              - { label: "Currency", name: "currency", widget: "string", default: "INR" }
              - { label: "Description", name: "description", widget: "string" }
              - { label: "Offer Label", name: "offerLabel", widget: "string" }
              - { label: "Features", name: "features", widget: "list", field: { label: "Feature", name: "feature", widget: "string" } }
              - { label: "Button Text", name: "buttonText", widget: "string" }
              - { label: "Button Link", name: "buttonLink", widget: "string" }

          - label: "Benefits"
            name: "benefits"
            widget: "list"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Description", name: "description", widget: "text" }
              - { label: "Value", name: "value", widget: "string" }

          - label: "Registration Process"
            name: "process"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Steps", name: "steps", widget: "list", field: { label: "Step", name: "step", widget: "string" } }

          - label: "Contact Information"
            name: "contactInfo"
            widget: "object"
            fields:
              - { label: "Phone", name: "phone", widget: "string" }
              - { label: "Email", name: "email", widget: "string" }
              - { label: "Address", name: "address", widget: "text" }
              - { label: "Working Hours", name: "workingHours", widget: "string" }

  - name: "pricing"
    label: "Pricing Page"
    files:
      - file: "src/content/pricing/content.json"
        label: "Pricing Content"
        name: "content"
        fields:
          - label: "Hero Section"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Subtitle", name: "subtitle", widget: "string" }

          - label: "Pricing Plans"
            name: "plans"
            widget: "list"
            fields:
              - { label: "Plan Name", name: "name", widget: "string" }
              - { label: "Price", name: "price", widget: "number" }
              - { label: "Currency", name: "currency", widget: "string", default: "INR" }
              - { label: "Period", name: "period", widget: "string" }
              - { label: "Popular", name: "popular", widget: "boolean", default: false }
              - { label: "Description", name: "description", widget: "string" }
              - { label: "Features", name: "features", widget: "list", field: { label: "Feature", name: "feature", widget: "string" } }
              - { label: "Button Text", name: "buttonText", widget: "string" }
              - { label: "Button Link", name: "buttonLink", widget: "string" }

          - label: "Price Breakdown"
            name: "priceBreakdown"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Base Amount", name: "baseAmount", widget: "number" }
              - label: "GST"
                name: "gst"
                widget: "object"
                fields:
                  - { label: "Rate (%)", name: "rate", widget: "number" }
                  - { label: "Amount", name: "amount", widget: "number" }
              - label: "Platform Fee"
                name: "platformFee"
                widget: "object"
                fields:
                  - { label: "Rate (%)", name: "rate", widget: "number" }
                  - { label: "Amount", name: "amount", widget: "number" }
              - { label: "Total", name: "total", widget: "number" }
              - { label: "Currency", name: "currency", widget: "string", default: "INR" }

          - label: "Guarantee"
            name: "guarantee"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Description", name: "description", widget: "text" }
              - { label: "Terms", name: "terms", widget: "string" }

          - label: "Support"
            name: "support"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Description", name: "description", widget: "text" }
              - { label: "Features", name: "features", widget: "list", field: { label: "Feature", name: "feature", widget: "string" } }

          - label: "Additional Information"
            name: "additionalInfo"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Description", name: "description", widget: "text" }
              - { label: "Highlights", name: "highlights", widget: "list", field: { label: "Highlight", name: "highlight", widget: "string" } }

  - name: "data"
    label: "Website Data"
    files:
      - file: "src/data/darviContentData.json"
        label: "Darvi Content Data"
        name: "content"
        fields:
          - label: "Company Info"
            name: "companyInfo"
            widget: "object"
            fields:
              - { label: "Name", name: "name", widget: "string" }
              - { label: "Founded", name: "founded", widget: "string" }
              - { label: "Location", name: "location", widget: "text" }
              - { label: "Contact", name: "contact", widget: "string" }
              - { label: "Email", name: "email", widget: "string" }
              - { label: "Website", name: "website", widget: "string" }
              - { label: "Store", name: "store", widget: "string" }
              - { label: "IoT Portal", name: "iotPortal", widget: "string" }
              - { label: "Working Hours", name: "workingHours", widget: "string" }
              - { label: "GST Number", name: "gstNumber", widget: "string", required: false }

          - label: "Team Members"
            name: "team"
            widget: "list"
            fields:
              - { label: "Name", name: "name", widget: "string" }
              - { label: "Position", name: "position", widget: "string" }
              - { label: "Image", name: "image", widget: "image", allow_multiple: false }
              - { label: "Education", name: "education", widget: "string" }
              - { label: "Specialization", name: "specialization", widget: "string" }
              - { label: "Experience", name: "experience", widget: "text" }
              - { label: "Current Project", name: "currentProject", widget: "string", required: false }
              - { label: "Short Description", name: "shortDescription", widget: "text" }
              - { label: "Full Description", name: "fullDescription", widget: "text", required: false }
              - { label: "Background", name: "background", widget: "text", required: false }
              - { label: "Mission", name: "mission", widget: "text", required: false }

          - label: "Mission & Values"
            name: "missionValues"
            widget: "object"
            fields:
              - { label: "Mission", name: "mission", widget: "text" }
              - { label: "Values", name: "values", widget: "list", field: { label: "Value", name: "value", widget: "string" } }
              - { label: "Commitment", name: "commitment", widget: "text" }

          - label: "Products"
            name: "products"
            widget: "list"
            fields:
              - { label: "Name", name: "name", widget: "string" }
              - { label: "Image", name: "image", widget: "image", allow_multiple: false, required: false }
              - { label: "Price", name: "price", widget: "string", required: false }
              - { label: "Benefits", name: "benefits", widget: "text" }
              - { label: "Best For", name: "bestFor", widget: "text" }
              - { label: "Application", name: "application", widget: "text" }
              - { label: "Results", name: "results", widget: "text" }
              - { label: "Composition", name: "composition", widget: "text" }

          - label: "Success Stories"
            name: "successStories"
            widget: "list"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Image", name: "image", widget: "image", allow_multiple: false, required: false }
              - { label: "Challenge", name: "challenge", widget: "text" }
              - { label: "Solution", name: "solution", widget: "text" }
              - { label: "Results", name: "results", widget: "text" }
              - { label: "Timeline", name: "timeline", widget: "string", required: false }
              - { label: "ROI", name: "roi", widget: "string", required: false }
              - { label: "Sustainability", name: "sustainability", widget: "text", required: false }

          - label: "Registration Services"
            name: "registrationServices"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string" }
              - { label: "Subtitle", name: "subtitle", widget: "string" }
              - label: "Registration Fee"
                name: "registrationFee"
                widget: "object"
                fields:
                  - { label: "Amount", name: "amount", widget: "number" }
                  - { label: "Currency", name: "currency", widget: "string", default: "INR" }
                  - { label: "Description", name: "description", widget: "string" }
              - label: "Pricing"
                name: "pricing"
                widget: "object"
                fields:
                  - { label: "Title", name: "title", widget: "string" }
                  - { label: "Subtitle", name: "subtitle", widget: "string" }
                  - { label: "Guarantee", name: "guarantee", widget: "string" }
                  - { label: "Support", name: "support", widget: "string" }
              - label: "Benefits"
                name: "benefits"
                widget: "list"
                fields:
                  - { label: "Title", name: "title", widget: "string" }
                  - { label: "Description", name: "description", widget: "text" }
                  - { label: "Value", name: "value", widget: "string" }
              - label: "Registration Process"
                name: "registrationProcess"
                widget: "list"
                field: { label: "Step", name: "step", widget: "string" }
              - label: "Contact Info"
                name: "contactInfo"
                widget: "object"
                fields:
                  - { label: "Phone", name: "phone", widget: "string" }
                  - { label: "Email", name: "email", widget: "string" }
                  - { label: "Address", name: "address", widget: "text" }
                  - { label: "Working Hours", name: "workingHours", widget: "string" }

  - name: "checkout"
    label: "Checkout Page"
    files:
      - file: "src/content/checkout/content.json"
        label: "Checkout Content"
        name: "content"
        fields:
          - label: "Hero Section"
            name: "hero"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Subtitle", name: "subtitle", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}

          - label: "Payment Information"
            name: "paymentInfo"
            widget: "object"
            fields:
              - { label: "Amount", name: "amount", widget: "number" }
              - { label: "Currency", name: "currency", widget: "string", default: "INR" }
              - { label: "Description", name: "description", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Features Included", name: "featuresIncluded", widget: "list", field: { label: "Feature", name: "feature", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}}

          - label: "Price Breakdown"
            name: "priceBreakdown"
            widget: "object"
            fields:
              - { label: "Show Breakdown", name: "showBreakdown", widget: "boolean", default: true }
              - { label: "Base Amount", name: "baseAmount", widget: "number" }
              - { label: "GST Rate", name: "gstRate", widget: "number", default: 5 }
              - { label: "Platform Fee Rate", name: "platformFeeRate", widget: "number", default: 2 }
              - { label: "Total Amount", name: "totalAmount", widget: "number" }

          - label: "Payment Button"
            name: "paymentButton"
            widget: "object"
            fields:
              - { label: "Text", name: "text", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Loading Text", name: "loadingText", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}

          - label: "Security Information"
            name: "securityInfo"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string" },
                  { label: "Kannada", name: "kn", widget: "string" },
                  { label: "Hindi", name: "hi", widget: "string" }
                ]}
              - { label: "Description", name: "description", widget: "object", fields: [
                  { label: "English", name: "en", widget: "text" },
                  { label: "Kannada", name: "kn", widget: "text" },
                  { label: "Hindi", name: "hi", widget: "text" }
                ]}

  - name: "legal"
    label: "Legal Pages"
    files:
      - file: "src/content/legal/terms.json"
        label: "Terms & Conditions"
        name: "terms"
        fields:
          - { label: "Title", name: "title", widget: "string" }
          - { label: "Last Updated", name: "lastUpdated", widget: "datetime" }
          - label: "Sections"
            name: "sections"
            widget: "list"
            fields:
              - { label: "Heading", name: "heading", widget: "string" }
              - { label: "Content", name: "content", widget: "markdown" }

      - file: "src/content/legal/privacy.json"
        label: "Privacy Policy"
        name: "privacy"
        fields:
          - { label: "Title", name: "title", widget: "string" }
          - { label: "Last Updated", name: "lastUpdated", widget: "datetime" }
          - label: "Sections"
            name: "sections"
            widget: "list"
            fields:
              - { label: "Heading", name: "heading", widget: "string" }
              - { label: "Content", name: "content", widget: "markdown" }

      - file: "src/content/legal/refund.json"
        label: "Refund Policy"
        name: "refund"
        fields:
          - { label: "Title", name: "title", widget: "string" }
          - { label: "Last Updated", name: "lastUpdated", widget: "datetime" }
          - label: "Sections"
            name: "sections"
            widget: "list"
            fields:
              - { label: "Heading", name: "heading", widget: "string" }
              - { label: "Content", name: "content", widget: "markdown" }

  - name: "ui-text"
    label: "UI Text & Labels"
    files:
      - file: "src/content/ui/buttons.json"
        label: "Button Text"
        name: "buttons"
        fields:
          - label: "Common Buttons"
            name: "common"
            widget: "object"
            fields:
              - { label: "Submit", name: "submit", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Submit" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಸಲ್ಲಿಸಿ" },
                  { label: "Hindi", name: "hi", widget: "string", default: "जमा करें" }
                ]}
              - { label: "Cancel", name: "cancel", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Cancel" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ರದ್ದುಮಾಡಿ" },
                  { label: "Hindi", name: "hi", widget: "string", default: "रद्द करें" }
                ]}
              - { label: "Continue", name: "continue", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Continue" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಮುಂದುವರಿಸಿ" },
                  { label: "Hindi", name: "hi", widget: "string", default: "जारी रखें" }
                ]}
              - { label: "Learn More", name: "learnMore", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Learn More" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಇನ್ನಷ್ಟು ತಿಳಿಯಿರಿ" },
                  { label: "Hindi", name: "hi", widget: "string", default: "और जानें" }
                ]}
              - { label: "Get Started", name: "getStarted", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Get Started" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಪ್ರಾರಂಭಿಸಿ" },
                  { label: "Hindi", name: "hi", widget: "string", default: "शुरू करें" }
                ]}

          - label: "Navigation Buttons"
            name: "navigation"
            widget: "object"
            fields:
              - { label: "Back", name: "back", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Back" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಹಿಂದೆ" },
                  { label: "Hindi", name: "hi", widget: "string", default: "वापस" }
                ]}
              - { label: "Next", name: "next", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Next" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಮುಂದೆ" },
                  { label: "Hindi", name: "hi", widget: "string", default: "अगला" }
                ]}
              - { label: "Home", name: "home", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Home" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಮುಖ್ಯಪುಟ" },
                  { label: "Hindi", name: "hi", widget: "string", default: "होम" }
                ]}

      - file: "src/content/ui/messages.json"
        label: "Messages & Notifications"
        name: "messages"
        fields:
          - label: "Success Messages"
            name: "success"
            widget: "object"
            fields:
              - { label: "Form Submitted", name: "formSubmitted", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Form submitted successfully!" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಫಾರ್ಮ್ ಯಶಸ್ವಿಯಾಗಿ ಸಲ್ಲಿಸಲಾಗಿದೆ!" },
                  { label: "Hindi", name: "hi", widget: "string", default: "फॉर्म सफलतापूर्वक जमा किया गया!" }
                ]}
              - { label: "Payment Success", name: "paymentSuccess", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Payment completed successfully!" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಪಾವತಿ ಯಶಸ್ವಿಯಾಗಿ ಪೂರ್ಣಗೊಂಡಿದೆ!" },
                  { label: "Hindi", name: "hi", widget: "string", default: "भुगतान सफलतापूर्वक पूरा हुआ!" }
                ]}

          - label: "Error Messages"
            name: "error"
            widget: "object"
            fields:
              - { label: "Generic Error", name: "generic", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Something went wrong. Please try again." },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಏನೋ ತಪ್ಪಾಗಿದೆ. ದಯವಿಟ್ಟು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ." },
                  { label: "Hindi", name: "hi", widget: "string", default: "कुछ गलत हुआ। कृपया पुनः प्रयास करें।" }
                ]}
              - { label: "Payment Failed", name: "paymentFailed", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Payment failed. Please try again." },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಪಾವತಿ ವಿಫಲವಾಗಿದೆ. ದಯವಿಟ್ಟು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ." },
                  { label: "Hindi", name: "hi", widget: "string", default: "भुगतान विफल। कृपया पुनः प्रयास करें।" }
                ]}

          - label: "Loading Messages"
            name: "loading"
            widget: "object"
            fields:
              - { label: "Please Wait", name: "pleaseWait", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Please wait..." },
                  { label: "Kannada", name: "kn", widget: "string", default: "ದಯವಿಟ್ಟು ನಿರೀಕ್ಷಿಸಿ..." },
                  { label: "Hindi", name: "hi", widget: "string", default: "कृपया प्रतीक्षा करें..." }
                ]}
              - { label: "Processing", name: "processing", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Processing..." },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಪ್ರಕ್ರಿಯೆಗೊಳಿಸಲಾಗುತ್ತಿದೆ..." },
                  { label: "Hindi", name: "hi", widget: "string", default: "प्रसंस्करण..." }
                ]}

      - file: "src/content/ui/labels.json"
        label: "Form Labels & Placeholders"
        name: "labels"
        fields:
          - label: "Common Form Fields"
            name: "common"
            widget: "object"
            fields:
              - { label: "Name", name: "name", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Name" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಹೆಸರು" },
                  { label: "Hindi", name: "hi", widget: "string", default: "नाम" }
                ]}
              - { label: "Email", name: "email", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Email" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಇಮೇಲ್" },
                  { label: "Hindi", name: "hi", widget: "string", default: "ईमेल" }
                ]}
              - { label: "Phone", name: "phone", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Phone" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ಫೋನ್" },
                  { label: "Hindi", name: "hi", widget: "string", default: "फोन" }
                ]}
              - { label: "Address", name: "address", widget: "object", fields: [
                  { label: "English", name: "en", widget: "string", default: "Address" },
                  { label: "Kannada", name: "kn", widget: "string", default: "ವಿಳಾಸ" },
                  { label: "Hindi", name: "hi", widget: "string", default: "पता" }
                ]}

  - name: "seo"
    label: "SEO & Meta Data"
    files:
      - file: "src/content/seo/meta.json"
        label: "Page Meta Data"
        name: "meta"
        fields:
          - label: "Home Page"
            name: "home"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string", default: "Darvi Group - Agricultural Services Platform" }
              - { label: "Description", name: "description", widget: "text", default: "Comprehensive agricultural solutions and farmer registration platform. Expert consultation, soil health assessment, and sustainable farming practices." }
              - { label: "Keywords", name: "keywords", widget: "string", default: "agriculture, farming, consultation, soil health, sustainable farming, Karnataka" }
              - { label: "OG Image", name: "ogImage", widget: "image", required: false }

  # New comprehensive CMS collections
  - name: "components"
    label: "Components"
    files:
      - file: "src/content/ui/buttons.json"
        label: "Buttons & UI Elements"
        name: "buttons"
        fields:
          - label: "Primary Buttons"
            name: "primary"
            widget: "object"
            fields:
              - { label: "Submit Text", name: "submit", widget: "string", default: "Submit" }
              - { label: "Register Text", name: "register", widget: "string", default: "Register Now" }
              - { label: "Contact Text", name: "contact", widget: "string", default: "Contact Us" }
              - { label: "Learn More Text", name: "learnMore", widget: "string", default: "Learn More" }
              - { label: "Get Started Text", name: "getStarted", widget: "string", default: "Get Started" }
          - label: "Secondary Buttons"
            name: "secondary"
            widget: "object"
            fields:
              - { label: "Cancel Text", name: "cancel", widget: "string", default: "Cancel" }
              - { label: "Back Text", name: "back", widget: "string", default: "Back" }
              - { label: "Skip Text", name: "skip", widget: "string", default: "Skip" }
          - label: "Loading States"
            name: "loading"
            widget: "object"
            fields:
              - { label: "Processing Text", name: "processing", widget: "string", default: "Processing..." }
              - { label: "Loading Text", name: "loading", widget: "string", default: "Loading..." }
              - { label: "Submitting Text", name: "submitting", widget: "string", default: "Submitting..." }

      - file: "src/content/ui/messages.json"
        label: "Messages & Notifications"
        name: "messages"
        fields:
          - label: "Success Messages"
            name: "success"
            widget: "object"
            fields:
              - { label: "Form Submitted", name: "formSubmitted", widget: "string", default: "Form submitted successfully!" }
              - { label: "Payment Success", name: "paymentSuccess", widget: "string", default: "Payment completed successfully!" }
              - { label: "Registration Success", name: "registrationSuccess", widget: "string", default: "Registration completed successfully!" }
          - label: "Error Messages"
            name: "error"
            widget: "object"
            fields:
              - { label: "Form Error", name: "formError", widget: "string", default: "Please fill all required fields." }
              - { label: "Payment Error", name: "paymentError", widget: "string", default: "Payment failed. Please try again." }
              - { label: "Network Error", name: "networkError", widget: "string", default: "Network error. Please check your connection." }
          - label: "Validation Messages"
            name: "validation"
            widget: "object"
            fields:
              - { label: "Required Field", name: "required", widget: "string", default: "This field is required." }
              - { label: "Invalid Email", name: "invalidEmail", widget: "string", default: "Please enter a valid email address." }
              - { label: "Invalid Phone", name: "invalidPhone", widget: "string", default: "Please enter a valid phone number." }

      - file: "src/content/ui/labels.json"
        label: "Form Labels & Text"
        name: "labels"
        fields:
          - label: "Form Fields"
            name: "fields"
            widget: "object"
            fields:
              - { label: "Name Label", name: "name", widget: "string", default: "Full Name" }
              - { label: "Email Label", name: "email", widget: "string", default: "Email Address" }
              - { label: "Phone Label", name: "phone", widget: "string", default: "Phone Number" }
              - { label: "Address Label", name: "address", widget: "string", default: "Address" }
              - { label: "City Label", name: "city", widget: "string", default: "City" }
              - { label: "Pincode Label", name: "pincode", widget: "string", default: "Pincode" }
              - { label: "Land Area Label", name: "landArea", widget: "string", default: "Land Area (in acres)" }
              - { label: "Soil Type Label", name: "soilType", widget: "string", default: "Soil Type" }
              - { label: "District Label", name: "district", widget: "string", default: "District" }
              - { label: "Taluk Label", name: "taluk", widget: "string", default: "Taluk" }
          - label: "Placeholders"
            name: "placeholders"
            widget: "object"
            fields:
              - { label: "Name Placeholder", name: "name", widget: "string", default: "Enter your full name" }
              - { label: "Email Placeholder", name: "email", widget: "string", default: "Enter your email address" }
              - { label: "Phone Placeholder", name: "phone", widget: "string", default: "Enter your phone number" }
              - { label: "Address Placeholder", name: "address", widget: "string", default: "Enter your address" }

  - name: "settings"
    label: "Website Settings"
    files:
      - file: "src/config/cms-settings.json"
        label: "General Settings"
        name: "general"
        fields:
          - label: "Site Information"
            name: "site"
            widget: "object"
            fields:
              - { label: "Site Title", name: "title", widget: "string", default: "Darvi Group" }
              - { label: "Site Description", name: "description", widget: "text", default: "Agricultural Services Platform" }
              - { label: "Site URL", name: "url", widget: "string", default: "https://darvigroup.in" }
              - { label: "Logo", name: "logo", widget: "image", required: false }
              - { label: "Favicon", name: "favicon", widget: "image", required: false }
          - label: "Contact Information"
            name: "contact"
            widget: "object"
            fields:
              - { label: "Phone Number", name: "phone", widget: "string", default: "+91 99868 90777" }
              - { label: "Email Address", name: "email", widget: "string", default: "<EMAIL>" }
              - { label: "Address", name: "address", widget: "text", default: "#2 Totad building, Arjun Vihar Gokul road Hubli, Karnataka 580030, India" }
              - { label: "Working Hours", name: "workingHours", widget: "string", default: "Monday - Saturday, 9:00 AM - 6:00 PM" }
              - { label: "GST Number", name: "gstNumber", widget: "string", default: "29ABCDE1234F1Z5" }
          - label: "Social Media"
            name: "social"
            widget: "object"
            fields:
              - { label: "Facebook URL", name: "facebook", widget: "string", required: false }
              - { label: "Twitter URL", name: "twitter", widget: "string", required: false }
              - { label: "Instagram URL", name: "instagram", widget: "string", required: false }
              - { label: "LinkedIn URL", name: "linkedin", widget: "string", required: false }
              - { label: "YouTube URL", name: "youtube", widget: "string", required: false }

      - file: "src/config/payment-settings.json"
        label: "Payment Settings"
        name: "payment"
        fields:
          - label: "Registration Fee"
            name: "registration"
            widget: "object"
            fields:
              - { label: "Base Amount", name: "baseAmount", widget: "number", default: 4500 }
              - { label: "GST Percentage", name: "gstPercentage", widget: "number", default: 5 }
              - { label: "Total Amount", name: "totalAmount", widget: "number", default: 4725 }
              - { label: "Currency", name: "currency", widget: "string", default: "INR" }
              - { label: "Currency Symbol", name: "currencySymbol", widget: "string", default: "₹" }
          - label: "Payment Gateway"
            name: "gateway"
            widget: "object"
            fields:
              - { label: "Gateway Name", name: "name", widget: "string", default: "PayU" }
              - { label: "Test Mode", name: "testMode", widget: "boolean", default: false }
              - { label: "Timeout (seconds)", name: "timeout", widget: "number", default: 300 }
          - label: "Payment Methods"
            name: "methods"
            widget: "object"
            fields:
              - { label: "Enable Credit Cards", name: "creditCards", widget: "boolean", default: true }
              - { label: "Enable Debit Cards", name: "debitCards", widget: "boolean", default: true }
              - { label: "Enable Net Banking", name: "netBanking", widget: "boolean", default: true }
              - { label: "Enable UPI", name: "upi", widget: "boolean", default: true }
              - { label: "Enable Wallets", name: "wallets", widget: "boolean", default: true }

      - file: "src/config/form-settings.json"
        label: "Form Settings"
        name: "forms"
        fields:
          - label: "Registration Form"
            name: "registration"
            widget: "object"
            fields:
              - { label: "Enable Address Field", name: "enableAddress", widget: "boolean", default: true }
              - { label: "Enable City Field", name: "enableCity", widget: "boolean", default: true }
              - { label: "Enable Pincode Field", name: "enablePincode", widget: "boolean", default: true }
              - { label: "Require Land Area", name: "requireLandArea", widget: "boolean", default: true }
              - { label: "Require Soil Type", name: "requireSoilType", widget: "boolean", default: true }
              - { label: "Require District", name: "requireDistrict", widget: "boolean", default: true }
              - { label: "Require Taluk", name: "requireTaluk", widget: "boolean", default: true }
          - label: "Validation Rules"
            name: "validation"
            widget: "object"
            fields:
              - { label: "Min Name Length", name: "minNameLength", widget: "number", default: 2 }
              - { label: "Max Name Length", name: "maxNameLength", widget: "number", default: 50 }
              - { label: "Phone Number Pattern", name: "phonePattern", widget: "string", default: "^[6-9]\\d{9}$" }
              - { label: "Email Required", name: "emailRequired", widget: "boolean", default: true }
              - { label: "Phone Required", name: "phoneRequired", widget: "boolean", default: true }

  - name: "receipt"
    label: "Receipt Settings"
    files:
      - file: "src/config/receipt-settings.json"
        label: "Receipt Configuration"
        name: "receipt"
        fields:
          - label: "Receipt Header"
            name: "header"
            widget: "object"
            fields:
              - { label: "Company Name", name: "companyName", widget: "string", default: "DARVI GROUP" }
              - { label: "Receipt Title", name: "title", widget: "string", default: "Farmer Registration Payment Receipt" }
              - { label: "GST Number", name: "gstNumber", widget: "string", default: "29ABCDE1234F1Z5" }
              - { label: "Website", name: "website", widget: "string", default: "www.darvigroup.in" }
              - { label: "Phone", name: "phone", widget: "string", default: "+91 99868 90777" }
          - label: "Receipt Footer"
            name: "footer"
            widget: "object"
            fields:
              - { label: "Thank You Message", name: "thankYou", widget: "string", default: "Thank you for choosing Darvi Group!" }
              - { label: "Success Message", name: "successMessage", widget: "string", default: "Your registration has been successfully completed." }
              - { label: "Support Message", name: "supportMessage", widget: "string", default: "For support, contact us:" }
              - { label: "Tagline", name: "tagline", widget: "string", default: "Empowering farmers with sustainable agricultural solutions" }
              - { label: "Signature Note", name: "signatureNote", widget: "string", default: "This is a computer-generated receipt. No signature required." }
          - label: "Receipt Styling"
            name: "styling"
            widget: "object"
            fields:
              - { label: "Primary Color", name: "primaryColor", widget: "string", default: "#2E7D32" }
              - { label: "Secondary Color", name: "secondaryColor", widget: "string", default: "#4CAF50" }
              - { label: "Font Family", name: "fontFamily", widget: "string", default: "Arial, sans-serif" }
              - { label: "Font Size", name: "fontSize", widget: "number", default: 14 }

  - name: "email"
    label: "Email Templates"
    files:
      - file: "src/config/email-templates.json"
        label: "Email Templates"
        name: "templates"
        fields:
          - label: "Registration Confirmation"
            name: "registrationConfirmation"
            widget: "object"
            fields:
              - { label: "Subject", name: "subject", widget: "string", default: "Registration Successful - Darvi Group" }
              - { label: "Greeting", name: "greeting", widget: "string", default: "Dear {{customerName}}," }
              - { label: "Body", name: "body", widget: "markdown", default: "Thank you for registering with Darvi Group. Your registration has been successfully completed." }
              - { label: "Closing", name: "closing", widget: "string", default: "Best regards,\nDarvi Group Team" }
          - label: "Payment Confirmation"
            name: "paymentConfirmation"
            widget: "object"
            fields:
              - { label: "Subject", name: "subject", widget: "string", default: "Payment Received - Darvi Group" }
              - { label: "Greeting", name: "greeting", widget: "string", default: "Dear {{customerName}}," }
              - { label: "Body", name: "body", widget: "markdown", default: "We have received your payment of ₹{{amount}}. Your receipt is attached." }
              - { label: "Closing", name: "closing", widget: "string", default: "Best regards,\nDarvi Group Team" }
          - label: "Welcome Email"
            name: "welcome"
            widget: "object"
            fields:
              - { label: "Subject", name: "subject", widget: "string", default: "Welcome to Darvi Group!" }
              - { label: "Greeting", name: "greeting", widget: "string", default: "Welcome {{customerName}}!" }
              - { label: "Body", name: "body", widget: "markdown", default: "Welcome to Darvi Group! We're excited to help you with your agricultural needs." }
              - { label: "Closing", name: "closing", widget: "string", default: "Best regards,\nDarvi Group Team" }

  - name: "features"
    label: "Feature Toggles"
    files:
      - file: "src/config/features.json"
        label: "Feature Flags"
        name: "flags"
        fields:
          - label: "Payment Features"
            name: "payment"
            widget: "object"
            fields:
              - { label: "Enable Payment Gateway", name: "enablePayment", widget: "boolean", default: true }
              - { label: "Enable Receipt Download", name: "enableReceipt", widget: "boolean", default: true }
              - { label: "Enable Email Notifications", name: "enableEmailNotifications", widget: "boolean", default: true }
              - { label: "Enable SMS Notifications", name: "enableSmsNotifications", widget: "boolean", default: false }
          - label: "Form Features"
            name: "form"
            widget: "object"
            fields:
              - { label: "Enable Multi-language", name: "enableMultiLanguage", widget: "boolean", default: true }
              - { label: "Enable Auto-save", name: "enableAutoSave", widget: "boolean", default: true }
              - { label: "Enable Progress Bar", name: "enableProgressBar", widget: "boolean", default: true }
              - { label: "Enable Field Validation", name: "enableValidation", widget: "boolean", default: true }
          - label: "UI Features"
            name: "ui"
            widget: "object"
            fields:
              - { label: "Enable Dark Mode", name: "enableDarkMode", widget: "boolean", default: false }
              - { label: "Enable Animations", name: "enableAnimations", widget: "boolean", default: true }
              - { label: "Enable Loading States", name: "enableLoadingStates", widget: "boolean", default: true }
              - { label: "Enable Tooltips", name: "enableTooltips", widget: "boolean", default: true }

  - name: "analytics"
    label: "Analytics & Tracking"
    files:
      - file: "src/config/analytics.json"
        label: "Analytics Configuration"
        name: "analytics"
        fields:
          - label: "Google Analytics"
            name: "googleAnalytics"
            widget: "object"
            fields:
              - { label: "Tracking ID", name: "trackingId", widget: "string", required: false }
              - { label: "Enable Enhanced Ecommerce", name: "enableEcommerce", widget: "boolean", default: false }
          - label: "Facebook Pixel"
            name: "facebookPixel"
            widget: "object"
            fields:
              - { label: "Pixel ID", name: "pixelId", widget: "string", required: false }
              - { label: "Enable Tracking", name: "enableTracking", widget: "boolean", default: false }
          - label: "Custom Events"
            name: "events"
            widget: "object"
            fields:
              - { label: "Track Form Submissions", name: "trackFormSubmissions", widget: "boolean", default: true }
              - { label: "Track Payment Events", name: "trackPaymentEvents", widget: "boolean", default: true }
              - { label: "Track Page Views", name: "trackPageViews", widget: "boolean", default: true }

          - label: "Services Page"
            name: "services"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string", default: "Agricultural Services - Darvi Group" }
              - { label: "Description", name: "description", widget: "text", default: "Comprehensive agricultural support services including expert consultation, crop recommendations, and soil health assessment." }
              - { label: "Keywords", name: "keywords", widget: "string", default: "agricultural services, farming consultation, crop recommendations, soil assessment" }

          - label: "Pricing Page"
            name: "pricing"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string", default: "Pricing - Darvi Group Registration" }
              - { label: "Description", name: "description", widget: "text", default: "Transparent pricing for comprehensive agricultural support. One-time registration fee for lifetime membership benefits." }
              - { label: "Keywords", name: "keywords", widget: "string", default: "agricultural pricing, farmer registration, membership, consultation fees" }

          - label: "About Page"
            name: "about"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string", default: "About Darvi Group - Agricultural Experts" }
              - { label: "Description", name: "description", widget: "text", default: "Learn about Darvi Group's mission to revolutionize agricultural practices through sustainable solutions and expert guidance." }
              - { label: "Keywords", name: "keywords", widget: "string", default: "about darvi group, agricultural experts, sustainable farming, company mission" }

          - label: "Contact Page"
            name: "contact"
            widget: "object"
            fields:
              - { label: "Title", name: "title", widget: "string", default: "Contact Darvi Group - Agricultural Support" }
              - { label: "Description", name: "description", widget: "text", default: "Get in touch with Darvi Group for agricultural consultation and support. Contact our experts for personalized farming solutions." }
              - { label: "Keywords", name: "keywords", widget: "string", default: "contact darvi group, agricultural support, farming consultation, expert advice" }
