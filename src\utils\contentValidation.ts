import * as Yup from 'yup';

// Validation schemas for different content types
export const blogPostValidationSchema = Yup.object({
  title: Yup.object({
    en: Yup.string().required('English title is required').min(5, 'Title must be at least 5 characters'),
    kn: Yup.string().min(5, 'Kannada title must be at least 5 characters'),
    hi: Yup.string().min(5, 'Hindi title must be at least 5 characters')
  }),
  excerpt: Yup.object({
    en: Yup.string().required('English excerpt is required').min(20, 'Excerpt must be at least 20 characters').max(200, 'Excerpt must be less than 200 characters'),
    kn: Yup.string().min(20, 'Kannada excerpt must be at least 20 characters').max(200, 'Kannada excerpt must be less than 200 characters'),
    hi: Yup.string().min(20, 'Hindi excerpt must be at least 20 characters').max(200, 'Hindi excerpt must be less than 200 characters')
  }),
  content: Yup.object({
    en: Yup.string().required('English content is required').min(100, 'Content must be at least 100 characters'),
    kn: Yup.string().min(100, 'Kannada content must be at least 100 characters'),
    hi: Yup.string().min(100, 'Hindi content must be at least 100 characters')
  }),
  category: Yup.string().required('Category is required'),
  author: Yup.object({
    en: Yup.string().required('English author name is required'),
    kn: Yup.string(),
    hi: Yup.string()
  }),
  date: Yup.string().required('Publication date is required'),
  image: Yup.string().required('Featured image is required').url('Image must be a valid URL'),
  tags: Yup.array().of(Yup.string()).min(1, 'At least one tag is required').max(10, 'Maximum 10 tags allowed'),
  seoTitle: Yup.string().max(60, 'SEO title must be less than 60 characters'),
  seoDescription: Yup.string().max(160, 'SEO description must be less than 160 characters')
});

export const mediaFileValidationSchema = Yup.object({
  name: Yup.string().required('File name is required').min(1, 'File name cannot be empty'),
  alt: Yup.string().when('type', {
    is: 'image',
    then: (schema) => schema.required('Alt text is required for images').min(5, 'Alt text must be at least 5 characters'),
    otherwise: (schema) => schema
  }),
  description: Yup.string().max(500, 'Description must be less than 500 characters'),
  tags: Yup.array().of(Yup.string()).max(20, 'Maximum 20 tags allowed'),
  size: Yup.number().max(10485760, 'File size must be less than 10MB') // 10MB in bytes
});

export const registrationFormValidationSchema = Yup.object({
  name: Yup.string()
    .required('Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  mobile: Yup.string()
    .required('Mobile number is required')
    .matches(/^[6-9]\d{9}$/, 'Please enter a valid 10-digit mobile number starting with 6-9'),
  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address')
    .max(100, 'Email must be less than 100 characters'),
  address: Yup.string()
    .required('Address is required')
    .min(10, 'Address must be at least 10 characters')
    .max(200, 'Address must be less than 200 characters'),
  city: Yup.string()
    .required('City is required')
    .min(2, 'City must be at least 2 characters')
    .max(50, 'City must be less than 50 characters'),
  state: Yup.string()
    .required('State is required')
    .min(2, 'State must be at least 2 characters'),
  district: Yup.string()
    .required('District is required')
    .min(2, 'District must be at least 2 characters'),
  taluk: Yup.string()
    .required('Taluk is required')
    .min(2, 'Taluk must be at least 2 characters'),
  landArea: Yup.string()
    .required('Land area is required')
    .matches(/^\d+(\.\d+)?\s*(acres?|hectares?|sq\.?\s*ft\.?|sq\.?\s*m\.?)$/i, 'Please enter a valid land area (e.g., "5 acres", "2.5 hectares")'),
  soilType: Yup.string()
    .required('Soil type is required'),
  termsAccepted: Yup.boolean()
    .oneOf([true], 'You must accept the terms and conditions')
});

export const contactFormValidationSchema = Yup.object({
  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address'),
  contactNumber: Yup.string()
    .required('Contact number is required')
    .matches(/^[6-9]\d{9}$/, 'Please enter a valid 10-digit mobile number'),
  message: Yup.string()
    .required('Message is required')
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message must be less than 1000 characters')
});

// Content validation rules
export interface ValidationRule {
  field: string;
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  value?: any;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export const contentValidationRules: Record<string, ValidationRule[]> = {
  blogPost: [
    {
      field: 'title.en',
      type: 'required',
      message: 'English title is required',
      severity: 'error'
    },
    {
      field: 'title.en',
      type: 'minLength',
      value: 5,
      message: 'Title should be at least 5 characters long',
      severity: 'error'
    },
    {
      field: 'excerpt.en',
      type: 'required',
      message: 'English excerpt is required',
      severity: 'error'
    },
    {
      field: 'excerpt.en',
      type: 'maxLength',
      value: 200,
      message: 'Excerpt should be less than 200 characters',
      severity: 'warning'
    },
    {
      field: 'content.en',
      type: 'required',
      message: 'English content is required',
      severity: 'error'
    },
    {
      field: 'content.en',
      type: 'minLength',
      value: 100,
      message: 'Content should be at least 100 characters long',
      severity: 'error'
    },
    {
      field: 'image',
      type: 'required',
      message: 'Featured image is required',
      severity: 'error'
    },
    {
      field: 'tags',
      type: 'custom',
      message: 'At least one tag is recommended for better discoverability',
      severity: 'warning'
    }
  ],
  mediaFile: [
    {
      field: 'name',
      type: 'required',
      message: 'File name is required',
      severity: 'error'
    },
    {
      field: 'alt',
      type: 'required',
      message: 'Alt text is required for accessibility',
      severity: 'error'
    },
    {
      field: 'description',
      type: 'maxLength',
      value: 500,
      message: 'Description should be less than 500 characters',
      severity: 'warning'
    }
  ]
};

// Validation utility functions
export const validateField = (value: any, rules: ValidationRule[]): { isValid: boolean; errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  rules.forEach(rule => {
    let isValid = true;

    switch (rule.type) {
      case 'required':
        isValid = value !== null && value !== undefined && value !== '';
        break;
      case 'minLength':
        isValid = typeof value === 'string' && value.length >= rule.value;
        break;
      case 'maxLength':
        isValid = typeof value === 'string' && value.length <= rule.value;
        break;
      case 'pattern':
        isValid = typeof value === 'string' && new RegExp(rule.value).test(value);
        break;
      case 'custom':
        // Custom validation logic can be implemented here
        isValid = true;
        break;
    }

    if (!isValid) {
      if (rule.severity === 'error') {
        errors.push(rule.message);
      } else if (rule.severity === 'warning') {
        warnings.push(rule.message);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

export const validateContent = (content: any, contentType: string): { isValid: boolean; errors: string[]; warnings: string[] } => {
  const rules = contentValidationRules[contentType] || [];
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  rules.forEach(rule => {
    const fieldValue = getNestedValue(content, rule.field);
    const validation = validateField(fieldValue, [rule]);
    
    allErrors.push(...validation.errors);
    allWarnings.push(...validation.warnings);
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
};

// Helper function to get nested object values
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

// Error handling utilities
export class ContentValidationError extends Error {
  public field: string;
  public code: string;

  constructor(message: string, field: string, code: string) {
    super(message);
    this.name = 'ContentValidationError';
    this.field = field;
    this.code = code;
  }
}

export const handleValidationError = (error: any): { message: string; field?: string; code?: string } => {
  if (error instanceof ContentValidationError) {
    return {
      message: error.message,
      field: error.field,
      code: error.code
    };
  }

  if (error.name === 'ValidationError') {
    return {
      message: error.message,
      field: error.path,
      code: 'VALIDATION_ERROR'
    };
  }

  return {
    message: error.message || 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR'
  };
};

// Form submission error handling
export const handleFormSubmissionError = (error: any): string => {
  if (error.response?.status === 400) {
    return 'Please check your input and try again.';
  }
  
  if (error.response?.status === 401) {
    return 'You are not authorized to perform this action.';
  }
  
  if (error.response?.status === 403) {
    return 'Access denied. Please contact an administrator.';
  }
  
  if (error.response?.status === 404) {
    return 'The requested resource was not found.';
  }
  
  if (error.response?.status === 422) {
    return 'The data provided is invalid. Please check your input.';
  }
  
  if (error.response?.status >= 500) {
    return 'A server error occurred. Please try again later.';
  }
  
  if (error.code === 'NETWORK_ERROR') {
    return 'Network error. Please check your internet connection.';
  }
  
  return error.message || 'An unexpected error occurred. Please try again.';
};

// Content sanitization
export const sanitizeContent = (content: string): string => {
  // Basic HTML sanitization - in production, use a library like DOMPurify
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');
};

// Input validation helpers
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^[6-9]\d{9}$/;
  return phoneRegex.test(phone);
};

export const isValidURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const isValidImageFile = (file: File): boolean => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  return allowedTypes.includes(file.type) && file.size <= maxSize;
};

export const isValidDocumentFile = (file: File): boolean => {
  const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  return allowedTypes.includes(file.type) && file.size <= maxSize;
};
