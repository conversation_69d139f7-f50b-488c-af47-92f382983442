import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Snackbar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Visibility as PreviewIcon,
  Home as HomeIcon,
  Info as AboutIcon,
  ContactMail as ContactIcon,
  Business as ServicesIcon,
  Article as BlogIcon,
  Image as ImageIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  Language as LanguageIcon
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';
import { useCMSContent } from '../hooks/useCMSContent';

interface ContentSection {
  id: string;
  name: string;
  type: 'text' | 'image' | 'list' | 'object';
  page: string;
  content: any;
  multilingual: boolean;
  required: boolean;
}

interface ContentPage {
  id: string;
  name: string;
  icon: React.ReactNode;
  sections: ContentSection[];
  description: string;
}

const ContentManager: React.FC = () => {
  const { language } = useLanguage();
  const [selectedPage, setSelectedPage] = useState<string>('home');
  const [editingSection, setEditingSection] = useState<ContentSection | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentTab, setCurrentTab] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>('hero');

  // Content pages configuration
  const contentPages: ContentPage[] = [
    {
      id: 'home',
      name: 'Home Page',
      icon: <HomeIcon />,
      description: 'Manage homepage content including hero section, features, and testimonials',
      sections: [
        {
          id: 'hero',
          name: 'Hero Section',
          type: 'object',
          page: 'home',
          content: {},
          multilingual: true,
          required: true
        },
        {
          id: 'features',
          name: 'Features Section',
          type: 'list',
          page: 'home',
          content: [],
          multilingual: true,
          required: false
        },
        {
          id: 'testimonials',
          name: 'Testimonials',
          type: 'list',
          page: 'home',
          content: [],
          multilingual: true,
          required: false
        }
      ]
    },
    {
      id: 'about',
      name: 'About Page',
      icon: <AboutIcon />,
      description: 'Manage about page content including company story and team information',
      sections: [
        {
          id: 'story',
          name: 'Company Story',
          type: 'text',
          page: 'about',
          content: '',
          multilingual: true,
          required: true
        },
        {
          id: 'mission',
          name: 'Mission Statement',
          type: 'text',
          page: 'about',
          content: '',
          multilingual: true,
          required: true
        },
        {
          id: 'values',
          name: 'Company Values',
          type: 'list',
          page: 'about',
          content: [],
          multilingual: true,
          required: false
        }
      ]
    },
    {
      id: 'services',
      name: 'Services Page',
      icon: <ServicesIcon />,
      description: 'Manage services and offerings',
      sections: [
        {
          id: 'services-list',
          name: 'Services List',
          type: 'list',
          page: 'services',
          content: [],
          multilingual: true,
          required: true
        },
        {
          id: 'pricing',
          name: 'Pricing Information',
          type: 'object',
          page: 'services',
          content: {},
          multilingual: true,
          required: false
        }
      ]
    },
    {
      id: 'contact',
      name: 'Contact Page',
      icon: <ContactIcon />,
      description: 'Manage contact information and form settings',
      sections: [
        {
          id: 'contact-info',
          name: 'Contact Information',
          type: 'object',
          page: 'contact',
          content: {},
          multilingual: true,
          required: true
        },
        {
          id: 'office-locations',
          name: 'Office Locations',
          type: 'list',
          page: 'contact',
          content: [],
          multilingual: true,
          required: false
        }
      ]
    }
  ];

  const getLocalizedText = (textObj: any) => {
    if (typeof textObj === 'string') return textObj;
    return textObj?.[language as keyof typeof textObj] || textObj?.en || '';
  };

  const handlePageSelect = (pageId: string) => {
    setSelectedPage(pageId);
    setExpandedAccordion('hero');
  };

  const handleEditSection = (section: ContentSection) => {
    setEditingSection(section);
    setDialogOpen(true);
    setCurrentTab(0);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingSection(null);
    setCurrentTab(0);
  };

  const handleSaveSection = () => {
    // Implementation for saving section content
    setSnackbar({
      open: true,
      message: 'Content updated successfully!',
      severity: 'success'
    });
    handleCloseDialog();
  };

  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAccordion(isExpanded ? panel : false);
  };

  const selectedPageData = contentPages.find(page => page.id === selectedPage);

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {language === 'en' ? 'Content Management' :
         language === 'kn' ? 'ವಿಷಯ ನಿರ್ವಹಣೆ' :
         'सामग्री प्रबंधन'}
      </Typography>

      <Grid container spacing={3}>
        {/* Page Navigation */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {language === 'en' ? 'Pages' :
                 language === 'kn' ? 'ಪುಟಗಳು' :
                 'पृष्ठ'}
              </Typography>
              <List>
                {contentPages.map((page) => (
                  <ListItem
                    key={page.id}
                    button
                    selected={selectedPage === page.id}
                    onClick={() => handlePageSelect(page.id)}
                  >
                    <ListItemIcon>
                      {page.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={page.name}
                      secondary={page.description}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Content Sections */}
        <Grid item xs={12} md={9}>
          {selectedPageData && (
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  {selectedPageData.icon}
                  <Typography variant="h5" sx={{ ml: 1 }}>
                    {selectedPageData.name}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {selectedPageData.description}
                </Typography>

                {selectedPageData.sections.map((section) => (
                  <Accordion
                    key={section.id}
                    expanded={expandedAccordion === section.id}
                    onChange={handleAccordionChange(section.id)}
                  >
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <Typography variant="h6" sx={{ flexGrow: 1 }}>
                          {section.name}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          {section.multilingual && (
                            <Chip
                              icon={<LanguageIcon />}
                              label="Multilingual"
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          )}
                          {section.required && (
                            <Chip
                              label="Required"
                              size="small"
                              color="error"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          Type: {section.type} | Page: {section.page}
                        </Typography>
                        <Box>
                          <IconButton
                            onClick={() => handleEditSection(section)}
                            color="primary"
                            size="small"
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton color="info" size="small">
                            <PreviewIcon />
                          </IconButton>
                        </Box>
                      </Box>
                    </AccordionDetails>
                  </Accordion>
                ))}
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>

      {/* Edit Section Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { height: '80vh' }
        }}
      >
        <DialogTitle>
          {language === 'en' ? 'Edit Content Section' :
           language === 'kn' ? 'ವಿಷಯ ವಿಭಾಗವನ್ನು ಸಂಪಾದಿಸಿ' :
           'सामग्री अनुभाग संपादित करें'}
          {editingSection && `: ${editingSection.name}`}
        </DialogTitle>
        <DialogContent>
          {editingSection?.multilingual ? (
            <>
              <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
                <Tab label="English" />
                <Tab label="ಕನ್ನಡ" />
                <Tab label="हिंदी" />
              </Tabs>
              <Box sx={{ mt: 2 }}>
                {/* Content editing forms will be implemented based on section type */}
                <TextField
                  fullWidth
                  label={`Content (${currentTab === 0 ? 'English' : currentTab === 1 ? 'Kannada' : 'Hindi'})`}
                  multiline
                  rows={6}
                  variant="outlined"
                />
              </Box>
            </>
          ) : (
            <Box sx={{ mt: 2 }}>
              <TextField
                fullWidth
                label="Content"
                multiline
                rows={6}
                variant="outlined"
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<CancelIcon />}>
            {language === 'en' ? 'Cancel' :
             language === 'kn' ? 'ರದ್ದುಮಾಡಿ' :
             'रद्द करें'}
          </Button>
          <Button onClick={handleSaveSection} variant="contained" startIcon={<SaveIcon />}>
            {language === 'en' ? 'Save' :
             language === 'kn' ? 'ಉಳಿಸಿ' :
             'सेव करें'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ContentManager;
