import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Grid,
  TextField,
  Switch,
  FormControlLabel,
  Button,
  Alert,
  Divider,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
  Article as ArticleIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { useCMS } from '../contexts/CMSContext';
import ImageUpload from './ImageUpload';
import ContentEditor from './ContentEditor';
import BlogPostManager from './BlogPostManager';
import ContentManager from './ContentManager';
import MediaManager from './MediaManager';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`cms-tabpanel-${index}`}
      aria-labelledby={`cms-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const CMSAdmin: React.FC = () => {
  const { cmsSettings, paymentSettings, receiptSettings, uiContent, featureFlags, loading, error } = useCMS();
  const [activeTab, setActiveTab] = useState(0);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [imageManagerOpen, setImageManagerOpen] = useState(false);
  const [contentEditorOpen, setContentEditorOpen] = useState(false);
  const [selectedContent, setSelectedContent] = useState<any>(null);
  const [images, setImages] = useState<any[]>([]);

  // Local state for editable settings
  const [localPaymentSettings, setLocalPaymentSettings] = useState(paymentSettings);
  const [localFeatureFlags, setLocalFeatureFlags] = useState(featureFlags);

  // Update local state when CMS data changes
  React.useEffect(() => {
    setLocalPaymentSettings(paymentSettings);
    setLocalFeatureFlags(featureFlags);
  }, [paymentSettings, featureFlags]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Update functions for settings
  const updatePaymentSettings = (path: string, value: any) => {
    setLocalPaymentSettings(prev => {
      if (!prev) return prev;
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current: any = newSettings;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newSettings;
    });
  };

  const updateFeatureFlags = (path: string, value: any) => {
    setLocalFeatureFlags(prev => {
      if (!prev) return prev;
      const newFlags = { ...prev };
      const keys = path.split('.');
      let current: any = newFlags;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newFlags;
    });
  };

  const handleSave = async () => {
    setSaveStatus('saving');
    try {
      // In a real implementation, this would save to your CMS backend
      // For now, we'll just simulate a save
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (err) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  // Handle content editing
  const handleEditContent = (contentType: string, content: any) => {
    setSelectedContent({ type: contentType, data: content });
    setContentEditorOpen(true);
  };

  // Handle content save
  const handleContentSave = async (data: any) => {
    // In a real implementation, this would save to your CMS backend
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('Saving content:', data);
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  // Handle image management
  const handleImageManager = () => {
    setImageManagerOpen(true);
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading CMS content...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">Error loading CMS content: {error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Content Management System
      </Typography>
      
      {saveStatus === 'saved' && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Content saved successfully!
        </Alert>
      )}
      
      {saveStatus === 'error' && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Failed to save content. Please try again.
        </Alert>
      )}

      <Paper sx={{ width: '100%' }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="CMS tabs" variant="scrollable">
          <Tab label="Page Content" icon={<ArticleIcon />} />
          <Tab label="Content Management" icon={<ArticleIcon />} />
          <Tab label="Blog Management" icon={<ArticleIcon />} />
          <Tab label="Image Library" icon={<ImageIcon />} />
          <Tab label="Site Settings" icon={<SettingsIcon />} />
          <Tab label="Payment Settings" />
          <Tab label="Receipt Settings" />
          <Tab label="UI Content" />
          <Tab label="Feature Flags" />
        </Tabs>

        {/* Page Content Tab */}
        <TabPanel value={activeTab} index={0}>
          <ContentManager />
        </TabPanel>

        {/* Content Management Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Content Management</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <Card>
                    <CardHeader title="Home Page" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Manage hero sections, features, and content
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => handleEditContent('home', {})}
                        fullWidth
                      >
                        Edit Content
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <Card>
                    <CardHeader title="About Page" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Company information and team details
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => handleEditContent('about', {})}
                        fullWidth
                      >
                        Edit Content
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <Card>
                    <CardHeader title="Services Page" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Service offerings and descriptions
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => handleEditContent('services', {})}
                        fullWidth
                      >
                        Edit Content
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <Card>
                    <CardHeader title="Blog Posts" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Manage blog articles and categories
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => handleEditContent('blog', {})}
                        fullWidth
                      >
                        Edit Content
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <Card>
                    <CardHeader title="Contact Page" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Contact information and forms
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => handleEditContent('contact', {})}
                        fullWidth
                      >
                        Edit Content
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <Card>
                    <CardHeader title="IoT Solutions" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        IoT products and features
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => handleEditContent('iot', {})}
                        fullWidth
                      >
                        Edit Content
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Blog Management Tab */}
        <TabPanel value={activeTab} index={2}>
          <BlogPostManager />
        </TabPanel>

        {/* Image Library Tab */}
        <TabPanel value={activeTab} index={3}>
          <MediaManager />
        </TabPanel>

        {/* Site Settings Tab */}
        <TabPanel value={activeTab} index={4}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Site Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Site Title"
                value={cmsSettings?.site.title || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Site URL"
                value={cmsSettings?.site.url || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Site Description"
                value={cmsSettings?.site.description || ''}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Contact Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={cmsSettings?.contact.phone || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email Address"
                value={cmsSettings?.contact.email || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Address"
                value={cmsSettings?.contact.address || ''}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Payment Settings Tab */}
        <TabPanel value={activeTab} index={5}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Registration Fee</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label="Base Amount"
                value={paymentSettings?.registration.baseAmount || 0}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label="GST Percentage"
                value={paymentSettings?.registration.gstPercentage || 0}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label="Total Amount"
                value={paymentSettings?.registration.totalAmount || 0}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Payment Methods</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localPaymentSettings?.methods.creditCards || false}
                    onChange={(e) => updatePaymentSettings('methods.creditCards', e.target.checked)}
                  />
                }
                label="Enable Credit Cards"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localPaymentSettings?.methods.debitCards || false}
                    onChange={(e) => updatePaymentSettings('methods.debitCards', e.target.checked)}
                  />
                }
                label="Enable Debit Cards"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localPaymentSettings?.methods.netBanking || false}
                    onChange={(e) => updatePaymentSettings('methods.netBanking', e.target.checked)}
                  />
                }
                label="Enable Net Banking"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localPaymentSettings?.methods.upi || false}
                    onChange={(e) => updatePaymentSettings('methods.upi', e.target.checked)}
                  />
                }
                label="Enable UPI"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Receipt Settings Tab */}
        <TabPanel value={activeTab} index={6}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Receipt Header</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Company Name"
                value={receiptSettings?.header.companyName || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Receipt Title"
                value={receiptSettings?.header.title || ''}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Receipt Footer</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Thank You Message"
                value={receiptSettings?.footer.thankYou || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Tagline"
                value={receiptSettings?.footer.tagline || ''}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* UI Content Tab */}
        <TabPanel value={activeTab} index={7}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Button Text" />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Submit Button"
                        value={uiContent?.buttons.primary.submit || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Register Button"
                        value={uiContent?.buttons.primary.register || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Contact Button"
                        value={uiContent?.buttons.primary.contact || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Messages" />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Form Submitted"
                        value={uiContent?.messages.success.formSubmitted || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Payment Success"
                        value={uiContent?.messages.success.paymentSuccess || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Payment Error"
                        value={uiContent?.messages.error.paymentError || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Feature Flags Tab */}
        <TabPanel value={activeTab} index={8}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Payment Features" />
                <CardContent>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localFeatureFlags?.payment.enablePayment || false}
                        onChange={(e) => updateFeatureFlags('payment.enablePayment', e.target.checked)}
                      />
                    }
                    label="Enable Payment Gateway"
                  />
                  <br />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localFeatureFlags?.payment.enableReceipt || false}
                        onChange={(e) => updateFeatureFlags('payment.enableReceipt', e.target.checked)}
                      />
                    }
                    label="Enable Receipt Download"
                  />
                  <br />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localFeatureFlags?.payment.enableEmailNotifications || false}
                        onChange={(e) => updateFeatureFlags('payment.enableEmailNotifications', e.target.checked)}
                      />
                    }
                    label="Enable Email Notifications"
                  />
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="UI Features" />
                <CardContent>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localFeatureFlags?.ui.enableAnimations || false}
                        onChange={(e) => updateFeatureFlags('ui.enableAnimations', e.target.checked)}
                      />
                    }
                    label="Enable Animations"
                  />
                  <br />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localFeatureFlags?.ui.enableLoadingStates || false}
                        onChange={(e) => updateFeatureFlags('ui.enableLoadingStates', e.target.checked)}
                      />
                    }
                    label="Enable Loading States"
                  />
                  <br />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localFeatureFlags?.ui.enableTooltips || false}
                        onChange={(e) => updateFeatureFlags('ui.enableTooltips', e.target.checked)}
                      />
                    }
                    label="Enable Tooltips"
                  />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <Box sx={{ p: 3, borderTop: 1, borderColor: 'divider' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSave}
            disabled={saveStatus === 'saving'}
          >
            {saveStatus === 'saving' ? 'Saving...' : 'Save Changes'}
          </Button>
        </Box>
      </Paper>

      {/* Content Editor Dialog */}
      <Dialog
        open={contentEditorOpen}
        onClose={() => setContentEditorOpen(false)}
        maxWidth="lg"
        fullWidth
        fullScreen
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Edit {selectedContent?.type} Content
            </Typography>
            <IconButton onClick={() => setContentEditorOpen(false)}>
              <DeleteIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {selectedContent && (
            <ContentEditor
              title={`${selectedContent.type} Content`}
              sections={[
                {
                  id: 'title',
                  type: 'text',
                  label: 'Title',
                  value: '',
                  multilingual: true,
                  required: true
                },
                {
                  id: 'description',
                  type: 'text',
                  label: 'Description',
                  value: '',
                  multilingual: true
                },
                {
                  id: 'image',
                  type: 'image',
                  label: 'Featured Image',
                  value: []
                }
              ]}
              onSave={handleContentSave}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Image Manager Dialog */}
      <Dialog
        open={imageManagerOpen}
        onClose={() => setImageManagerOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Image Library Manager</DialogTitle>
        <DialogContent>
          <ImageUpload
            images={images}
            onImagesChange={setImages}
            multiple={true}
            maxImages={50}
            showCategories={true}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImageManagerOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CMSAdmin;
