import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  Snackbar,
  Fab,
  Menu,
  MenuItem as MenuItemComponent,
  ListItemIcon,
  ListItemText,
  Checkbox,
  FormControlLabel,
  Divider,
  LinearProgress,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Upload as UploadIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Folder as FolderIcon,
  Image as ImageIcon,
  VideoLibrary as VideoIcon,
  AudioFile as AudioIcon,
  InsertDriveFile as FileIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';

interface MediaFile {
  id: string;
  name: string;
  type: 'image' | 'video' | 'audio' | 'document';
  url: string;
  thumbnail?: string;
  size: number;
  uploadDate: string;
  tags: string[];
  folder: string;
  alt?: string;
  description?: string;
}

interface MediaFolder {
  id: string;
  name: string;
  parentId?: string;
  createdDate: string;
}

const MediaManager: React.FC = () => {
  const { language } = useLanguage();
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [folders, setFolders] = useState<MediaFolder[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [currentFolder, setCurrentFolder] = useState<string>('root');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingFile, setEditingFile] = useState<MediaFile | null>(null);
  const [contextMenu, setContextMenu] = useState<{ mouseX: number; mouseY: number; fileId: string } | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // Sample data
  useEffect(() => {
    setMediaFiles([
      {
        id: '1',
        name: 'hero-image.jpg',
        type: 'image',
        url: '/darvi-images/field1.png',
        size: 1024000,
        uploadDate: '2024-01-15',
        tags: ['hero', 'agriculture', 'farming'],
        folder: 'root',
        alt: 'Agricultural field with crops',
        description: 'Main hero image for homepage'
      },
      {
        id: '2',
        name: 'team-photo.jpg',
        type: 'image',
        url: '/darvi-images/field2.jpg',
        size: 2048000,
        uploadDate: '2024-01-14',
        tags: ['team', 'about'],
        folder: 'root',
        alt: 'Team photo',
        description: 'Company team photograph'
      },
      {
        id: '3',
        name: 'iot-device.jpg',
        type: 'image',
        url: '/darvi-images/iot-device-agriculture.jpg',
        size: 1536000,
        uploadDate: '2024-01-13',
        tags: ['technology', 'iot', 'devices'],
        folder: 'root',
        alt: 'IoT device in agricultural field',
        description: 'Smart farming IoT device'
      }
    ]);

    setFolders([
      { id: 'root', name: 'Root', createdDate: '2024-01-01' },
      { id: 'images', name: 'Images', parentId: 'root', createdDate: '2024-01-01' },
      { id: 'documents', name: 'Documents', parentId: 'root', createdDate: '2024-01-01' },
      { id: 'blog', name: 'Blog Media', parentId: 'images', createdDate: '2024-01-01' }
    ]);
  }, []);

  const getLocalizedText = (key: string) => {
    const translations = {
      en: {
        mediaLibrary: 'Media Library',
        uploadFiles: 'Upload Files',
        search: 'Search media...',
        allTypes: 'All Types',
        images: 'Images',
        videos: 'Videos',
        documents: 'Documents',
        gridView: 'Grid View',
        listView: 'List View',
        selectAll: 'Select All',
        deselectAll: 'Deselect All',
        deleteSelected: 'Delete Selected',
        edit: 'Edit',
        delete: 'Delete',
        download: 'Download',
        share: 'Share',
        uploadSuccess: 'Files uploaded successfully!',
        deleteSuccess: 'Files deleted successfully!',
        updateSuccess: 'File updated successfully!'
      },
      kn: {
        mediaLibrary: 'ಮಾಧ್ಯಮ ಗ್ರಂಥಾಲಯ',
        uploadFiles: 'ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ',
        search: 'ಮಾಧ್ಯಮವನ್ನು ಹುಡುಕಿ...',
        allTypes: 'ಎಲ್ಲಾ ಪ್ರಕಾರಗಳು',
        images: 'ಚಿತ್ರಗಳು',
        videos: 'ವೀಡಿಯೊಗಳು',
        documents: 'ದಾಖಲೆಗಳು',
        gridView: 'ಗ್ರಿಡ್ ವೀಕ್ಷಣೆ',
        listView: 'ಪಟ್ಟಿ ವೀಕ್ಷಣೆ',
        selectAll: 'ಎಲ್ಲವನ್ನೂ ಆಯ್ಕೆಮಾಡಿ',
        deselectAll: 'ಎಲ್ಲವನ್ನೂ ಆಯ್ಕೆ ರದ್ದುಮಾಡಿ',
        deleteSelected: 'ಆಯ್ಕೆಮಾಡಿದವುಗಳನ್ನು ಅಳಿಸಿ',
        edit: 'ಸಂಪಾದಿಸಿ',
        delete: 'ಅಳಿಸಿ',
        download: 'ಡೌನ್‌ಲೋಡ್',
        share: 'ಹಂಚಿಕೊಳ್ಳಿ',
        uploadSuccess: 'ಫೈಲ್‌ಗಳನ್ನು ಯಶಸ್ವಿಯಾಗಿ ಅಪ್‌ಲೋಡ್ ಮಾಡಲಾಗಿದೆ!',
        deleteSuccess: 'ಫೈಲ್‌ಗಳನ್ನು ಯಶಸ್ವಿಯಾಗಿ ಅಳಿಸಲಾಗಿದೆ!',
        updateSuccess: 'ಫೈಲ್ ಅನ್ನು ಯಶಸ್ವಿಯಾಗಿ ನವೀಕರಿಸಲಾಗಿದೆ!'
      },
      hi: {
        mediaLibrary: 'मीडिया लाइब्रेरी',
        uploadFiles: 'फाइलें अपलोड करें',
        search: 'मीडिया खोजें...',
        allTypes: 'सभी प्रकार',
        images: 'चित्र',
        videos: 'वीडियो',
        documents: 'दस्तावेज़',
        gridView: 'ग्रिड व्यू',
        listView: 'लिस्ट व्यू',
        selectAll: 'सभी चुनें',
        deselectAll: 'सभी अचयनित करें',
        deleteSelected: 'चयनित को हटाएं',
        edit: 'संपादित करें',
        delete: 'हटाएं',
        download: 'डाउनलोड',
        share: 'साझा करें',
        uploadSuccess: 'फाइलें सफलतापूर्वक अपलोड हो गईं!',
        deleteSuccess: 'फाइलें सफलतापूर्वक हटा दी गईं!',
        updateSuccess: 'फाइल सफलतापूर्वक अपडेट हो गई!'
      }
    };
    return translations[language as keyof typeof translations]?.[key as keyof typeof translations.en] || key;
  };

  const filteredFiles = mediaFiles.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         file.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesType = filterType === 'all' || file.type === filterType;
    const matchesFolder = file.folder === currentFolder;
    return matchesSearch && matchesType && matchesFolder;
  });

  const handleFileSelect = (fileId: string) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  const handleSelectAll = () => {
    if (selectedFiles.length === filteredFiles.length) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(filteredFiles.map(file => file.id));
    }
  };

  const handleContextMenu = (event: React.MouseEvent, fileId: string) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      fileId
    });
  };

  const handleCloseContextMenu = () => {
    setContextMenu(null);
  };

  const handleEditFile = (file: MediaFile) => {
    setEditingFile(file);
    setEditDialogOpen(true);
    handleCloseContextMenu();
  };

  const handleDeleteFiles = (fileIds: string[]) => {
    setMediaFiles(prev => prev.filter(file => !fileIds.includes(file.id)));
    setSelectedFiles([]);
    setSnackbar({
      open: true,
      message: getLocalizedText('deleteSuccess'),
      severity: 'success'
    });
    handleCloseContextMenu();
  };

  const handleUploadFiles = async (files: FileList) => {
    setUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    for (let i = 0; i <= 100; i += 10) {
      setUploadProgress(i);
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Add uploaded files to the media library
    const newFiles: MediaFile[] = Array.from(files).map((file, index) => ({
      id: `upload-${Date.now()}-${index}`,
      name: file.name,
      type: file.type.startsWith('image/') ? 'image' : 
            file.type.startsWith('video/') ? 'video' :
            file.type.startsWith('audio/') ? 'audio' : 'document',
      url: URL.createObjectURL(file),
      size: file.size,
      uploadDate: new Date().toISOString().split('T')[0],
      tags: [],
      folder: currentFolder,
      alt: '',
      description: ''
    }));

    setMediaFiles(prev => [...prev, ...newFiles]);
    setUploading(false);
    setUploadProgress(0);
    setUploadDialogOpen(false);
    setSnackbar({
      open: true,
      message: getLocalizedText('uploadSuccess'),
      severity: 'success'
    });
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return <ImageIcon />;
      case 'video': return <VideoIcon />;
      case 'audio': return <AudioIcon />;
      default: return <FileIcon />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {getLocalizedText('mediaLibrary')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<UploadIcon />}
          onClick={() => setUploadDialogOpen(true)}
        >
          {getLocalizedText('uploadFiles')}
        </Button>
      </Box>

      {/* Toolbar */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <TextField
          placeholder={getLocalizedText('search')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          size="small"
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
          }}
          sx={{ minWidth: 250 }}
        />
        
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>{getLocalizedText('allTypes')}</InputLabel>
          <Select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            startAdornment={<FilterIcon sx={{ mr: 1 }} />}
          >
            <MenuItem value="all">{getLocalizedText('allTypes')}</MenuItem>
            <MenuItem value="image">{getLocalizedText('images')}</MenuItem>
            <MenuItem value="video">{getLocalizedText('videos')}</MenuItem>
            <MenuItem value="document">{getLocalizedText('documents')}</MenuItem>
          </Select>
        </FormControl>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title={getLocalizedText('gridView')}>
            <IconButton
              onClick={() => setViewMode('grid')}
              color={viewMode === 'grid' ? 'primary' : 'default'}
            >
              <GridViewIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title={getLocalizedText('listView')}>
            <IconButton
              onClick={() => setViewMode('list')}
              color={viewMode === 'list' ? 'primary' : 'default'}
            >
              <ListViewIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {selectedFiles.length > 0 && (
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              {selectedFiles.length} selected
            </Typography>
            <Button
              size="small"
              color="error"
              onClick={() => handleDeleteFiles(selectedFiles)}
            >
              {getLocalizedText('deleteSelected')}
            </Button>
          </Box>
        )}
      </Box>

      {/* Selection Controls */}
      <Box sx={{ mb: 2 }}>
        <FormControlLabel
          control={
            <Checkbox
              checked={selectedFiles.length === filteredFiles.length && filteredFiles.length > 0}
              indeterminate={selectedFiles.length > 0 && selectedFiles.length < filteredFiles.length}
              onChange={handleSelectAll}
            />
          }
          label={selectedFiles.length === filteredFiles.length ? getLocalizedText('deselectAll') : getLocalizedText('selectAll')}
        />
      </Box>

      {/* Media Grid */}
      <Grid container spacing={2}>
        {filteredFiles.map((file) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={file.id}>
            <Card
              sx={{
                cursor: 'pointer',
                border: selectedFiles.includes(file.id) ? 2 : 1,
                borderColor: selectedFiles.includes(file.id) ? 'primary.main' : 'divider',
                '&:hover': {
                  boxShadow: 3
                }
              }}
              onClick={() => handleFileSelect(file.id)}
              onContextMenu={(e) => handleContextMenu(e, file.id)}
            >
              {file.type === 'image' ? (
                <CardMedia
                  component="img"
                  height="140"
                  image={file.url}
                  alt={file.alt || file.name}
                  sx={{ objectFit: 'cover' }}
                />
              ) : (
                <Box
                  sx={{
                    height: 140,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'grey.100'
                  }}
                >
                  {getFileIcon(file.type)}
                </Box>
              )}
              <CardContent sx={{ pb: 1 }}>
                <Typography variant="subtitle2" noWrap>
                  {file.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {formatFileSize(file.size)} • {file.uploadDate}
                </Typography>
                <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {file.tags.slice(0, 2).map((tag) => (
                    <Chip key={tag} label={tag} size="small" variant="outlined" />
                  ))}
                  {file.tags.length > 2 && (
                    <Chip label={`+${file.tags.length - 2}`} size="small" variant="outlined" />
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={handleCloseContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItemComponent onClick={() => {
          const file = mediaFiles.find(f => f.id === contextMenu?.fileId);
          if (file) handleEditFile(file);
        }}>
          <ListItemIcon><EditIcon /></ListItemIcon>
          <ListItemText>{getLocalizedText('edit')}</ListItemText>
        </MenuItemComponent>
        <MenuItemComponent onClick={() => {
          if (contextMenu?.fileId) handleDeleteFiles([contextMenu.fileId]);
        }}>
          <ListItemIcon><DeleteIcon /></ListItemIcon>
          <ListItemText>{getLocalizedText('delete')}</ListItemText>
        </MenuItemComponent>
        <Divider />
        <MenuItemComponent>
          <ListItemIcon><DownloadIcon /></ListItemIcon>
          <ListItemText>{getLocalizedText('download')}</ListItemText>
        </MenuItemComponent>
        <MenuItemComponent>
          <ListItemIcon><ShareIcon /></ListItemIcon>
          <ListItemText>{getLocalizedText('share')}</ListItemText>
        </MenuItemComponent>
      </Menu>

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{getLocalizedText('uploadFiles')}</DialogTitle>
        <DialogContent>
          <Box
            sx={{
              border: '2px dashed',
              borderColor: 'primary.main',
              borderRadius: 2,
              p: 4,
              textAlign: 'center',
              cursor: 'pointer',
              '&:hover': {
                bgcolor: 'action.hover'
              }
            }}
            onClick={() => document.getElementById('file-upload')?.click()}
          >
            <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Drop files here or click to browse
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Supports images, videos, documents up to 10MB each
            </Typography>
            <input
              id="file-upload"
              type="file"
              multiple
              hidden
              onChange={(e) => {
                if (e.target.files) {
                  handleUploadFiles(e.target.files);
                }
              }}
            />
          </Box>
          {uploading && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress variant="determinate" value={uploadProgress} />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Uploading... {uploadProgress}%
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* Edit File Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit File Details</DialogTitle>
        <DialogContent>
          {editingFile && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
              <TextField
                label="File Name"
                value={editingFile.name}
                onChange={(e) => setEditingFile(prev => prev ? { ...prev, name: e.target.value } : null)}
                fullWidth
              />
              <TextField
                label="Alt Text"
                value={editingFile.alt || ''}
                onChange={(e) => setEditingFile(prev => prev ? { ...prev, alt: e.target.value } : null)}
                fullWidth
              />
              <TextField
                label="Description"
                value={editingFile.description || ''}
                onChange={(e) => setEditingFile(prev => prev ? { ...prev, description: e.target.value } : null)}
                multiline
                rows={3}
                fullWidth
              />
              <TextField
                label="Tags (comma separated)"
                value={editingFile.tags.join(', ')}
                onChange={(e) => setEditingFile(prev => prev ? { 
                  ...prev, 
                  tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                } : null)}
                fullWidth
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => {
              if (editingFile) {
                setMediaFiles(prev => prev.map(file => 
                  file.id === editingFile.id ? editingFile : file
                ));
                setSnackbar({
                  open: true,
                  message: getLocalizedText('updateSuccess'),
                  severity: 'success'
                });
                setEditDialogOpen(false);
              }
            }}
            variant="contained"
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default MediaManager;
